#!/bin/bash

# Face Recognition API Docker 部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_message "Docker 环境检查通过"
}

# 停止现有容器
stop_containers() {
    print_header "停止现有容器"
    
    if docker ps -q --filter "name=face-recognition" | grep -q .; then
        print_message "停止现有的 Face Recognition 容器..."
        docker-compose -f docker-compose.api.yml down
    else
        print_message "没有运行中的 Face Recognition 容器"
    fi
}

# 构建镜像
build_image() {
    print_header "构建 Docker 镜像"
    
    print_message "开始构建 Face Recognition API 镜像..."
    docker-compose -f docker-compose.api.yml build --no-cache
    
    print_message "镜像构建完成"
}

# 启动服务
start_services() {
    print_header "启动服务"
    
    # 创建必要的目录
    mkdir -p data
    
    # 启动服务
    print_message "启动 Face Recognition API 服务..."
    docker-compose -f docker-compose.api.yml up -d
    
    print_message "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker ps --filter "name=face-recognition-api" --filter "status=running" | grep -q face-recognition-api; then
        print_message "✅ Face Recognition API 服务启动成功"
    else
        print_error "❌ Face Recognition API 服务启动失败"
        docker-compose -f docker-compose.api.yml logs
        exit 1
    fi
}

# 启动带 Nginx 的服务
start_with_nginx() {
    print_header "启动服务 (包含 Nginx)"
    
    # 创建必要的目录
    mkdir -p data
    
    # 启动服务 (包含 Nginx)
    print_message "启动 Face Recognition API + Nginx 服务..."
    docker-compose -f docker-compose.api.yml --profile with-nginx up -d
    
    print_message "等待服务启动..."
    sleep 15
    
    # 检查服务状态
    if docker ps --filter "name=face-recognition-api" --filter "status=running" | grep -q face-recognition-api; then
        print_message "✅ Face Recognition API 服务启动成功"
    else
        print_error "❌ Face Recognition API 服务启动失败"
        exit 1
    fi
    
    if docker ps --filter "name=face-recognition-nginx" --filter "status=running" | grep -q face-recognition-nginx; then
        print_message "✅ Nginx 服务启动成功"
    else
        print_warning "⚠️  Nginx 服务启动失败，但 API 服务正常"
    fi
}

# 健康检查
health_check() {
    print_header "健康检查"
    
    print_message "等待服务完全启动..."
    sleep 5
    
    # 检查 API 健康状态
    for i in {1..10}; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            print_message "✅ API 服务健康检查通过"
            break
        else
            print_warning "等待 API 服务启动... ($i/10)"
            sleep 3
        fi
        
        if [ $i -eq 10 ]; then
            print_error "❌ API 服务健康检查失败"
            docker-compose -f docker-compose.api.yml logs face-recognition-api
            exit 1
        fi
    done
}

# 显示服务信息
show_info() {
    print_header "服务信息"
    
    echo -e "${GREEN}🎉 Face Recognition API 部署成功！${NC}"
    echo ""
    echo -e "${BLUE}访问地址:${NC}"
    echo -e "  📱 API 主页:     http://localhost:8000"
    echo -e "  📚 API 文档:     http://localhost:8000/docs"
    echo -e "  📖 ReDoc 文档:   http://localhost:8000/redoc"
    echo -e "  ❤️  健康检查:     http://localhost:8000/health"
    echo ""
    
    if docker ps --filter "name=face-recognition-nginx" --filter "status=running" | grep -q face-recognition-nginx; then
        echo -e "${BLUE}Nginx 代理 + 静态网页:${NC}"
        echo -e "  🏠 主页:         http://localhost"
        echo -e "  🧪 Web 测试页面: http://localhost/web_test_page.html"
        echo -e "  📚 API 文档:     http://localhost/docs"
        echo -e "  ❤️  健康检查:     http://localhost/health"
        echo ""
    fi
    
    echo -e "${BLUE}Docker 容器:${NC}"
    docker ps --filter "name=face-recognition" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    
    echo -e "${BLUE}管理命令:${NC}"
    echo -e "  查看日志: docker-compose -f docker-compose.api.yml logs -f"
    echo -e "  停止服务: docker-compose -f docker-compose.api.yml down"
    echo -e "  重启服务: docker-compose -f docker-compose.api.yml restart"
}

# 测试 API
test_api() {
    print_header "API 测试"
    
    print_message "测试 API 功能..."
    
    # 测试健康检查
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        print_message "✅ 健康检查测试通过"
    else
        print_error "❌ 健康检查测试失败"
    fi
    
    # 测试人脸检测 (如果有示例图片)
    if [ -f "examples/obama.jpg" ]; then
        print_message "测试人脸检测功能..."
        response=$(curl -s -X POST "http://localhost:8000/detect" -F "file=@examples/obama.jpg")
        if echo "$response" | grep -q '"success":true'; then
            print_message "✅ 人脸检测测试通过"
        else
            print_warning "⚠️  人脸检测测试失败，但服务正常运行"
        fi
    else
        print_warning "⚠️  没有找到测试图片，跳过人脸检测测试"
    fi
}

# 主函数
main() {
    print_header "Face Recognition API Docker 部署"
    
    case "${1:-deploy}" in
        "deploy")
            check_docker
            stop_containers
            build_image
            start_services
            health_check
            test_api
            show_info
            ;;
        "deploy-nginx")
            check_docker
            stop_containers
            build_image
            start_with_nginx
            health_check
            test_api
            show_info
            ;;
        "stop")
            print_header "停止服务"
            docker-compose -f docker-compose.api.yml down
            print_message "✅ 服务已停止"
            ;;
        "restart")
            print_header "重启服务"
            docker-compose -f docker-compose.api.yml restart
            health_check
            print_message "✅ 服务已重启"
            ;;
        "logs")
            print_header "查看日志"
            docker-compose -f docker-compose.api.yml logs -f
            ;;
        "status")
            print_header "服务状态"
            docker-compose -f docker-compose.api.yml ps
            ;;
        "clean")
            print_header "清理资源"
            docker-compose -f docker-compose.api.yml down -v
            docker system prune -f
            print_message "✅ 清理完成"
            ;;
        *)
            echo "用法: $0 [deploy|deploy-nginx|stop|restart|logs|status|clean]"
            echo ""
            echo "命令说明:"
            echo "  deploy       - 部署 API 服务 (默认)"
            echo "  deploy-nginx - 部署 API 服务 + Nginx"
            echo "  stop         - 停止服务"
            echo "  restart      - 重启服务"
            echo "  logs         - 查看日志"
            echo "  status       - 查看状态"
            echo "  clean        - 清理所有资源"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
