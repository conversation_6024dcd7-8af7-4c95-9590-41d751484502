version: '3.8'

services:
  face-recognition-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: face-recognition-api
    ports:
      - "8000:8000"
    volumes:
      # 挂载数据目录，实现数据持久化
      - ./data:/app/data
      # 挂载数据库文件 (如果存在)
      - ./face_database.json:/app/face_database.json
    environment:
      - PYTHONUNBUFFERED=1
      - FACE_DATABASE_FILE=/app/face_database.json
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - face-recognition-network

  # 可选：添加 Nginx 反向代理 + 静态文件服务
  nginx:
    image: nginx:alpine
    container_name: face-recognition-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./index.html:/usr/share/nginx/html/index.html:ro
      - ./web_test_page.html:/usr/share/nginx/html/web_test_page.html:ro
    depends_on:
      - face-recognition-api
    restart: unless-stopped
    networks:
      - face-recognition-network
    profiles:
      - with-nginx  # 使用 profile 使 nginx 可选

networks:
  face-recognition-network:
    driver: bridge

volumes:
  face-data:
    driver: local
