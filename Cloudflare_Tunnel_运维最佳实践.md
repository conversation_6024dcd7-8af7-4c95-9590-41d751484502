# Cloudflare Tunnel 运维最佳实践

## 🎯 运维目标

- **高可用性**：服务稳定运行，故障快速恢复
- **安全性**：访问控制和数据保护
- **性能优化**：响应速度和用户体验
- **成本控制**：资源合理利用

## 📋 日常运维检查清单

### 每日检查（5分钟）
- [ ] 隧道连接状态正常
- [ ] 服务响应时间正常
- [ ] 错误日志无异常
- [ ] 流量监控正常

```bash
# 每日检查脚本
#!/bin/bash
echo "=== Cloudflare Tunnel 每日检查 ==="
echo "检查时间: $(date)"

# 检查隧道状态
echo "1. 隧道状态检查:"
cloudflared tunnel list | grep -E "(CONNECTED|HEALTHY)" || echo "⚠️ 隧道状态异常"

# 检查服务响应
echo "2. 服务响应检查:"
for domain in face.juyingnj.com api.company.com; do
    if curl -s --max-time 10 "https://$domain/health" > /dev/null; then
        echo "✅ $domain 响应正常"
    else
        echo "❌ $domain 响应异常"
    fi
done

# 检查错误日志
echo "3. 错误日志检查:"
error_count=$(tail -100 /tmp/cloudflared.log | grep -c "ERR" || echo "0")
if [ "$error_count" -gt 5 ]; then
    echo "⚠️ 发现 $error_count 个错误，需要关注"
else
    echo "✅ 错误日志正常"
fi

echo "=== 检查完成 ==="
```

### 每周检查（15分钟）
- [ ] 证书有效期检查
- [ ] 性能指标分析
- [ ] 安全事件审查
- [ ] 配置备份验证

### 每月检查（30分钟）
- [ ] 流量趋势分析
- [ ] 成本使用评估
- [ ] 安全策略更新
- [ ] 灾备方案测试

## 🔧 配置管理最佳实践

### 1. 配置文件标准化

**目录结构**：
```
/etc/cloudflared/
├── config.yml          # 主配置文件
├── credentials/         # 凭证文件目录
│   └── tunnel-id.json
├── backup/             # 配置备份
└── scripts/            # 运维脚本
```

**配置模板**：
```yaml
# 标准配置模板
tunnel: ${TUNNEL_NAME}
credentials-file: /etc/cloudflared/credentials/${TUNNEL_ID}.json

ingress:
  - hostname: ${DOMAIN}
    service: ${LOCAL_SERVICE}
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s
      httpHostHeader: ${DOMAIN}
  - service: http_status:404

# 运维配置
logfile: /var/log/cloudflared/tunnel.log
loglevel: info
retries: 3
grace-period: 30s

# 性能优化
metrics: 0.0.0.0:2000
```

### 2. 版本控制

```bash
# 配置文件版本管理
git init /etc/cloudflared
cd /etc/cloudflared

# 添加 .gitignore
echo "credentials/" > .gitignore
echo "*.log" >> .gitignore

# 提交配置
git add config.yml scripts/
git commit -m "Initial cloudflared configuration"
```

### 3. 环境隔离

```yaml
# 开发环境
tunnel: dev-service
ingress:
  - hostname: dev.company.com
    service: http://localhost:8000

# 测试环境  
tunnel: test-service
ingress:
  - hostname: test.company.com
    service: http://localhost:8000

# 生产环境
tunnel: prod-service
ingress:
  - hostname: api.company.com
    service: http://localhost:8000
```

## 📊 监控和告警

### 1. 关键指标监控

**连接状态监控**：
```bash
#!/bin/bash
# 连接状态监控脚本

TUNNEL_NAME="your-tunnel"
WEBHOOK_URL="https://hooks.slack.com/your-webhook"

# 检查隧道连接
if ! cloudflared tunnel info $TUNNEL_NAME | grep -q "CONNECTED"; then
    # 发送告警
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"🚨 Cloudflare Tunnel 连接异常: '$TUNNEL_NAME'"}' \
        $WEBHOOK_URL
fi
```

**性能监控**：
```bash
# 响应时间监控
response_time=$(curl -o /dev/null -s -w '%{time_total}' https://your-domain.com/health)
if (( $(echo "$response_time > 2.0" | bc -l) )); then
    echo "⚠️ 响应时间过长: ${response_time}s"
fi
```

### 2. 日志分析

**日志轮转配置**：
```bash
# /etc/logrotate.d/cloudflared
/var/log/cloudflared/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 cloudflared cloudflared
    postrotate
        systemctl reload cloudflared
    endscript
}
```

**关键日志模式**：
```bash
# 监控关键错误
tail -f /var/log/cloudflared/tunnel.log | grep -E "(ERR|WARN|connection.*failed)"

# 连接统计
grep "Registered tunnel connection" /var/log/cloudflared/tunnel.log | wc -l
```

## 🛡️ 安全最佳实践

### 1. 访问控制

**IP 白名单**：
```yaml
ingress:
  - hostname: admin.company.com
    service: http://localhost:9000
    originRequest:
      # 仅允许办公网络访问
      ipRules:
        - "***********/24"
        - "10.0.0.0/8"
```

**Cloudflare Access 集成**：
```yaml
# 结合 Cloudflare Access 进行身份验证
ingress:
  - hostname: secure.company.com
    service: http://localhost:8000
    originRequest:
      # 启用 Access 验证
      cfAccess: true
```

### 2. 证书管理

```bash
# 证书有效期检查
check_cert_expiry() {
    domain=$1
    expiry_date=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    expiry_timestamp=$(date -d "$expiry_date" +%s)
    current_timestamp=$(date +%s)
    days_left=$(( (expiry_timestamp - current_timestamp) / 86400 ))
    
    if [ $days_left -lt 30 ]; then
        echo "⚠️ $domain 证书将在 $days_left 天后过期"
    fi
}
```

### 3. 凭证安全

```bash
# 凭证文件权限设置
chmod 600 /etc/cloudflared/credentials/*.json
chown cloudflared:cloudflared /etc/cloudflared/credentials/*.json

# 定期轮换凭证
rotate_credentials() {
    TUNNEL_NAME=$1
    # 创建新凭证
    cloudflared tunnel delete $TUNNEL_NAME
    cloudflared tunnel create $TUNNEL_NAME
    # 更新配置文件
    # 重启服务
}
```

## 🚀 性能优化

### 1. 连接优化

```yaml
originRequest:
  # 连接超时优化
  connectTimeout: 10s
  tlsTimeout: 5s
  
  # 连接复用
  keepAliveConnections: 100
  keepAliveTimeout: 90s
  tcpKeepAlive: 30s
  
  # HTTP 优化
  httpHostHeader: your-domain.com
  originServerName: your-domain.com
```

### 2. 缓存策略

```bash
# Cloudflare 页面规则配置
# 静态资源：缓存 1 个月
# API 响应：缓存 5 分钟
# 动态内容：不缓存
```

### 3. 负载均衡

```yaml
# 多隧道负载均衡
ingress:
  - hostname: api.company.com
    service: http://localhost:8000
    originRequest:
      # 启用负载均衡
      loadBalancer:
        - http://localhost:8001
        - http://localhost:8002
```

## 🔄 灾备和恢复

### 1. 备份策略

```bash
#!/bin/bash
# 配置备份脚本

BACKUP_DIR="/backup/cloudflared/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份配置文件
cp /etc/cloudflared/config.yml $BACKUP_DIR/
cp -r /etc/cloudflared/credentials $BACKUP_DIR/

# 备份隧道信息
cloudflared tunnel list > $BACKUP_DIR/tunnel-list.txt

# 压缩备份
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### 2. 快速恢复

```bash
#!/bin/bash
# 快速恢复脚本

restore_tunnel() {
    BACKUP_FILE=$1
    
    # 解压备份
    tar -xzf $BACKUP_FILE -C /tmp/
    
    # 恢复配置
    cp /tmp/cloudflared-*/config.yml /etc/cloudflared/
    cp -r /tmp/cloudflared-*/credentials /etc/cloudflared/
    
    # 重启服务
    systemctl restart cloudflared
    
    echo "恢复完成，请验证服务状态"
}
```

### 3. 故障切换

```bash
# 自动故障切换脚本
failover_tunnel() {
    PRIMARY_TUNNEL="primary-service"
    BACKUP_TUNNEL="backup-service"
    
    if ! cloudflared tunnel info $PRIMARY_TUNNEL | grep -q "CONNECTED"; then
        echo "主隧道故障，切换到备用隧道"
        # 更新 DNS 记录指向备用隧道
        # 发送告警通知
    fi
}
```

## 📈 成本优化

### 1. 流量分析

```bash
# 流量统计脚本
analyze_traffic() {
    # 从 Cloudflare Analytics API 获取数据
    curl -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/analytics/dashboard" \
         -H "Authorization: Bearer $API_TOKEN" \
         -H "Content-Type: application/json"
}
```

### 2. 资源优化

- **按需启动**：非工作时间自动停止开发环境隧道
- **流量压缩**：启用 Cloudflare 压缩功能
- **缓存优化**：合理设置缓存策略减少源站请求

## 📞 运维支持

### 应急响应流程
1. **故障发现** → 2. **影响评估** → 3. **应急处理** → 4. **根因分析** → 5. **预防改进**

### 联系方式
- **运维值班**：[值班电话]
- **技术群组**：[Slack/微信群]
- **工单系统**：[内部工单系统]

---

**文档版本**：v1.0  
**维护团队**：运维团队  
**更新周期**：月度更新
