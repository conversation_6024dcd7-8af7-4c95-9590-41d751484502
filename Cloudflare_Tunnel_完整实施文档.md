# Cloudflare Tunnel 完整实施文档

## 📋 概述

本文档详细介绍如何使用 Cloudflare Tunnel 将本地服务安全地暴露到公网，并绑定到自定义域名。以人脸识别 API 服务为例，演示完整的部署流程。

## 🎯 实施目标

- 将本地运行的 FastAPI 服务暴露到公网
- 绑定到固定域名（如：face.juyingnj.com）
- 实现安全、稳定的公网访问
- 支持 HTTPS 和自动证书管理

## 🔧 技术架构

```
本地服务 (localhost:8000) 
    ↓
Cloudflare Tunnel (cloudflared)
    ↓
Cloudflare Edge Network
    ↓
公网访问 (https://face.juyingnj.com)
```

## 📦 环境要求

### 系统要求
- macOS / Linux / Windows
- 网络连接正常
- 域名已托管在 Cloudflare

### 软件依赖
- cloudflared (Cloudflare Tunnel 客户端)
- 本地服务已运行（如 FastAPI 应用）

## 🚀 完整实施流程

### 第一步：安装 Cloudflare Tunnel

#### macOS (推荐使用 Homebrew)
```bash
# 安装 cloudflared
brew install cloudflared

# 验证安装
cloudflared --version
```

#### Linux
```bash
# 下载并安装
wget https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64
sudo mv cloudflared-linux-amd64 /usr/local/bin/cloudflared
sudo chmod +x /usr/local/bin/cloudflared
```

#### Windows
```powershell
# 使用 Chocolatey
choco install cloudflared

# 或下载 exe 文件直接安装
```

### 第二步：Cloudflare 账户认证

```bash
# 登录 Cloudflare 账户（会打开浏览器）
cloudflared tunnel login
```

**操作说明**：
1. 浏览器会自动打开 Cloudflare 登录页面
2. 登录您的 Cloudflare 账户
3. 选择要使用的域名
4. 授权 Tunnel 访问权限

### 第三步：创建命名隧道

```bash
# 创建隧道（替换为您的项目名称）
cloudflared tunnel create your-service-name

# 示例：创建人脸识别 API 隧道
cloudflared tunnel create face-recognition-api
```

**输出示例**：
```
Created tunnel face-recognition-api with id 7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d
Tunnel credentials written to /Users/<USER>/.cloudflared/7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d.json
```

**重要**：记录隧道 ID，后续配置需要使用。

### 第四步：创建配置文件

创建 `cloudflared-config.yml` 文件：

```yaml
# 隧道基本配置
tunnel: face-recognition-api
credentials-file: /Users/<USER>/.cloudflared/7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d.json

# 路由规则配置
ingress:
  # 主服务路由 - 绑定到自定义域名
  - hostname: face.juyingnj.com
    service: http://localhost:8000
    originRequest:
      # 连接优化设置
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s
  
  # 默认规则（必须存在）
  - service: http_status:404

# 全局配置
logfile: /tmp/cloudflared.log
loglevel: info
retries: 3
grace-period: 30s
```

**配置说明**：
- `tunnel`: 隧道名称
- `credentials-file`: 凭证文件路径（替换为实际路径）
- `hostname`: 要绑定的域名
- `service`: 本地服务地址
- `originRequest`: 连接优化参数

### 第五步：验证配置

```bash
# 验证配置文件语法
cloudflared tunnel --config cloudflared-config.yml ingress validate
```

预期输出：`OK`

### 第六步：配置 DNS 记录

#### 方法一：自动配置（推荐）
```bash
# 自动创建 DNS CNAME 记录
cloudflared tunnel route dns face-recognition-api face.juyingnj.com
```

#### 方法二：手动配置
如果自动配置失败，请在 Cloudflare 控制台手动添加：

1. 访问：https://dash.cloudflare.com
2. 选择域名：`juyingnj.com`
3. 进入 **DNS** 选项卡
4. 添加 CNAME 记录：
   ```
   类型: CNAME
   名称: face
   目标: 7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d.cfargotunnel.com
   代理状态: 已代理 (橙色云朵)
   TTL: 自动
   ```

### 第七步：创建启动脚本

创建 `start-tunnel.sh` 脚本：

```bash
#!/bin/bash

# 服务启动脚本
echo "🚀 启动 Cloudflare Tunnel..."
echo "🌐 域名: face.juyingnj.com"
echo "📡 本地服务: http://localhost:8000"
echo "❌ 按 Ctrl+C 停止隧道"
echo "----------------------------------------"

# 检查本地服务状态
if ! curl -s http://localhost:8000/health > /dev/null; then
    echo "⚠️  警告: 本地服务未运行，请先启动服务"
    exit 1
fi

# 启动隧道
cloudflared tunnel --config cloudflared-config.yml run face-recognition-api
```

```bash
# 添加执行权限
chmod +x start-tunnel.sh
```

### 第八步：启动隧道服务

```bash
# 方法一：使用脚本启动
./start-tunnel.sh

# 方法二：直接命令启动
cloudflared tunnel --config cloudflared-config.yml run face-recognition-api
```

**成功启动标志**：
```
INF Registered tunnel connection connIndex=0 connection=xxx event=0 ip=xxx location=xxx protocol=http2
```

## 🔍 验证和测试

### 1. DNS 解析验证
```bash
# 检查 DNS 记录
nslookup face.juyingnj.com
dig face.juyingnj.com
```

### 2. 服务可用性测试
```bash
# 健康检查
curl https://face.juyingnj.com/health

# API 文档访问
curl https://face.juyingnj.com/docs
```

### 3. 功能测试
```bash
# 人脸检测 API 测试
curl -X POST "https://face.juyingnj.com/detect" \
     -F "file=@test_image.jpg"
```

## 📊 监控和管理

### 查看隧道状态
```bash
# 列出所有隧道
cloudflared tunnel list

# 查看特定隧道信息
cloudflared tunnel info face-recognition-api

# 查看隧道连接状态
cloudflared tunnel --config cloudflared-config.yml ingress rule https://face.juyingnj.com
```

### 日志查看
```bash
# 查看隧道日志
tail -f /tmp/cloudflared.log

# 实时监控连接
cloudflared tunnel --config cloudflared-config.yml run face-recognition-api --loglevel debug
```

## 🔧 生产环境部署

### 系统服务配置（Linux systemd）

创建 `/etc/systemd/system/cloudflared.service`：

```ini
[Unit]
Description=Cloudflare Tunnel
After=network.target

[Service]
Type=simple
User=cloudflared
ExecStart=/usr/local/bin/cloudflared tunnel --config /etc/cloudflared/config.yml run
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
```

```bash
# 启用并启动服务
sudo systemctl enable cloudflared
sudo systemctl start cloudflared
sudo systemctl status cloudflared
```

### macOS LaunchDaemon 配置

创建 `/Library/LaunchDaemons/com.cloudflare.cloudflared.plist`：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.cloudflare.cloudflared</string>
    <key>ProgramArguments</key>
    <array>
        <string>/opt/homebrew/bin/cloudflared</string>
        <string>tunnel</string>
        <string>--config</string>
        <string>/etc/cloudflared/config.yml</string>
        <string>run</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
</dict>
</plist>
```

## 🛡️ 安全最佳实践

### 1. 访问控制
```yaml
# 在配置文件中添加访问策略
ingress:
  - hostname: face.juyingnj.com
    service: http://localhost:8000
    originRequest:
      # IP 白名单（可选）
      ipRules:
        - "***********/24"
        - "10.0.0.0/8"
```

### 2. 证书管理
- Cloudflare 自动提供 SSL/TLS 证书
- 支持 HTTP/2 和 HTTP/3
- 自动证书续期

### 3. DDoS 防护
- Cloudflare 提供自动 DDoS 防护
- 可配置速率限制
- 支持 WAF 规则

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 隧道连接失败
```bash
# 检查网络连接
ping *******

# 检查防火墙设置
sudo ufw status

# 尝试不同协议
cloudflared tunnel --protocol http2 run
```

#### 2. DNS 解析问题
```bash
# 清除 DNS 缓存
sudo dscacheutil -flushcache  # macOS
sudo systemctl restart systemd-resolved  # Linux

# 检查 DNS 传播
https://dnschecker.org/
```

#### 3. 本地服务无响应
```bash
# 检查服务状态
curl http://localhost:8000/health
netstat -tlnp | grep 8000

# 检查服务日志
tail -f /var/log/your-service.log
```

## 📈 性能优化

### 1. 连接优化
```yaml
originRequest:
  connectTimeout: 10s
  tlsTimeout: 10s
  tcpKeepAlive: 30s
  keepAliveConnections: 100
  keepAliveTimeout: 90s
  httpHostHeader: face.juyingnj.com
```

### 2. 缓存配置
```yaml
# Cloudflare 页面规则配置
- 静态资源缓存：1个月
- API 响应缓存：5分钟
- 动态内容：不缓存
```

## 💰 成本分析

### Cloudflare Tunnel 费用
- **免费版**：基础功能，适合个人项目
- **Pro 版**：$20/月，增强安全和性能
- **Business 版**：$200/月，企业级功能
- **Enterprise 版**：定制价格，完整功能

### 对比传统方案
| 方案 | 成本 | 安全性 | 易用性 | 维护成本 |
|------|------|--------|--------|----------|
| VPS + Nginx | $5-50/月 | 中等 | 复杂 | 高 |
| 云负载均衡 | $20-100/月 | 高 | 中等 | 中等 |
| Cloudflare Tunnel | $0-20/月 | 高 | 简单 | 低 |

## 📚 参考资源

### 官方文档
- [Cloudflare Tunnel 文档](https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/)
- [cloudflared CLI 参考](https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/tunnel-guide/)

### 社区资源
- [GitHub 仓库](https://github.com/cloudflare/cloudflared)
- [社区论坛](https://community.cloudflare.com/)
- [状态页面](https://www.cloudflarestatus.com/)

## 📞 技术支持

### 内部支持流程
1. 查阅本文档和官方文档
2. 检查常见问题解决方案
3. 联系运维团队
4. 提交 Cloudflare 支持工单

### 联系方式
- 内部技术群：[技术支持群]
- 运维邮箱：<EMAIL>
- 紧急联系：[值班电话]

---

**文档版本**：v1.0
**更新日期**：2025-06-17
**维护人员**：[您的姓名]
**审核状态**：待审核
