<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition API 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], input[type="text"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>🎭 Face Recognition API 测试页面</h1>

    <div class="container" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center;">
        <h2>🚀 Docker 部署版本</h2>
        <p>✅ 服务运行在容器中 | 📱 Web 界面通过 Nginx 提供 | 🔗 API 通过反向代理访问</p>
        <div style="display: flex; justify-content: center; gap: 20px; margin-top: 15px;">
            <a href="/docs" target="_blank" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 5px;">📚 API 文档</a>
            <a href="/health" target="_blank" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 5px;">❤️ 健康检查</a>
            <span style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 5px;">🐳 Docker 版本</span>
        </div>
    </div>
    
    <div class="container">
        <h2>🔍 人脸检测</h2>
        <div class="form-group">
            <label for="detectFile">选择图片:</label>
            <input type="file" id="detectFile" accept="image/webp,image/png,image/jpeg,image/jpg,image/gif,image/bmp">
        </div>
        <div class="form-group">
            <label for="detectModel">检测模型:</label>
            <select id="detectModel">
                <option value="hog">HOG (快速)</option>
                <option value="cnn">CNN (准确)</option>
            </select>
        </div>
        <button onclick="detectFaces()">检测人脸</button>
        <div id="detectResult" class="result" style="display: none;"></div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>➕ 添加人员</h2>
            <div class="form-group">
                <label for="addFile">选择图片:</label>
                <input type="file" id="addFile" accept="image/webp,image/png,image/jpeg,image/jpg,image/gif,image/bmp">
            </div>
            <div class="form-group">
                <label for="personName">姓名:</label>
                <input type="text" id="personName" placeholder="请输入姓名">
            </div>
            <div class="form-group">
                <label for="personDesc">描述:</label>
                <input type="text" id="personDesc" placeholder="请输入描述 (可选)">
            </div>
            <button onclick="addPerson()">添加人员</button>
            <div id="addResult" class="result" style="display: none;"></div>
        </div>

        <div class="container">
            <h2>📋 数据库管理</h2>
            <button onclick="viewDatabase()">查看数据库</button>
            <button onclick="clearDatabase()" style="background-color: #dc3545;">清空数据库</button>
            <div id="databaseResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>👤 人脸识别</h2>
        <div class="form-group">
            <label for="recognizeFile">选择图片:</label>
            <input type="file" id="recognizeFile" accept="image/webp,image/png,image/jpeg,image/jpg,image/gif,image/bmp">
        </div>
        <div class="form-group">
            <label for="tolerance">容忍度:</label>
            <select id="tolerance">
                <option value="0.4">0.4 (严格)</option>
                <option value="0.6" selected>0.6 (标准)</option>
                <option value="0.8">0.8 (宽松)</option>
            </select>
        </div>
        <button onclick="recognizeFaces()">识别人脸</button>
        <div id="recognizeResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>🔄 人脸比较</h2>
        <div class="grid">
            <div class="form-group">
                <label for="compareFile1">第一张图片:</label>
                <input type="file" id="compareFile1" accept="image/webp,image/png,image/jpeg,image/jpg,image/gif,image/bmp">
            </div>
            <div class="form-group">
                <label for="compareFile2">第二张图片:</label>
                <input type="file" id="compareFile2" accept="image/webp,image/png,image/jpeg,image/jpg,image/gif,image/bmp">
            </div>
        </div>
        <button onclick="compareFaces()">比较人脸</button>
        <div id="compareResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // 自动检测 API 基础 URL
        const API_BASE = window.location.protocol + '//' + window.location.host +
                         (window.location.port === '80' || window.location.port === '' ? '' : ':' + window.location.port);

        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.textContent = '处理中...';
        }

        async function detectFaces() {
            const fileInput = document.getElementById('detectFile');
            const model = document.getElementById('detectModel').value;
            
            if (!fileInput.files[0]) {
                alert('请选择图片文件');
                return;
            }

            showLoading('detectResult');

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('model', model);

            try {
                const response = await fetch(`${API_BASE}/detect`, {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                showResult('detectResult', data, data.success);
            } catch (error) {
                showResult('detectResult', {error: error.message}, false);
            }
        }

        async function addPerson() {
            const fileInput = document.getElementById('addFile');
            const name = document.getElementById('personName').value;
            const description = document.getElementById('personDesc').value;
            
            if (!fileInput.files[0] || !name) {
                alert('请选择图片文件并输入姓名');
                return;
            }

            showLoading('addResult');

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('name', name);
            formData.append('description', description);

            try {
                const response = await fetch(`${API_BASE}/database/add`, {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                showResult('addResult', data, data.success);
                
                // 清空表单
                if (data.success) {
                    document.getElementById('addFile').value = '';
                    document.getElementById('personName').value = '';
                    document.getElementById('personDesc').value = '';
                }
            } catch (error) {
                showResult('addResult', {error: error.message}, false);
            }
        }

        async function viewDatabase() {
            showLoading('databaseResult');

            try {
                const response = await fetch(`${API_BASE}/database`);
                const data = await response.json();
                showResult('databaseResult', data, data.success);
            } catch (error) {
                showResult('databaseResult', {error: error.message}, false);
            }
        }

        async function clearDatabase() {
            if (!confirm('确定要清空数据库吗？此操作不可恢复！')) {
                return;
            }

            showLoading('databaseResult');

            try {
                const response = await fetch(`${API_BASE}/database/clear`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                showResult('databaseResult', data, data.success);
            } catch (error) {
                showResult('databaseResult', {error: error.message}, false);
            }
        }

        async function recognizeFaces() {
            const fileInput = document.getElementById('recognizeFile');
            const tolerance = document.getElementById('tolerance').value;
            
            if (!fileInput.files[0]) {
                alert('请选择图片文件');
                return;
            }

            showLoading('recognizeResult');

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('tolerance', tolerance);

            try {
                const response = await fetch(`${API_BASE}/recognize`, {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                showResult('recognizeResult', data, data.success);
            } catch (error) {
                showResult('recognizeResult', {error: error.message}, false);
            }
        }

        async function compareFaces() {
            const file1Input = document.getElementById('compareFile1');
            const file2Input = document.getElementById('compareFile2');
            
            if (!file1Input.files[0] || !file2Input.files[0]) {
                alert('请选择两张图片文件');
                return;
            }

            showLoading('compareResult');

            const formData = new FormData();
            formData.append('file1', file1Input.files[0]);
            formData.append('file2', file2Input.files[0]);

            try {
                const response = await fetch(`${API_BASE}/compare`, {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                showResult('compareResult', data, data.success);
            } catch (error) {
                showResult('compareResult', {error: error.message}, false);
            }
        }

        // 页面加载时自动查看数据库
        window.onload = function() {
            viewDatabase();
        };
    </script>
</body>
</html>
