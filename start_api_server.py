#!/usr/bin/env python3
"""
Face Recognition API 服务器启动脚本
"""

import uvicorn
import sys
import os

def main():
    """启动 FastAPI 服务器"""
    print("🚀 启动 Face Recognition API 服务器...")
    print("📚 API 文档: http://localhost:8000/docs")
    print("📖 ReDoc 文档: http://localhost:8000/redoc")
    print("🏠 主页: http://localhost:8000")
    print("❌ 按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "face_recognition_api:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
