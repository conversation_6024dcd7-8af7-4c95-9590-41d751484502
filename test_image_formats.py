#!/usr/bin/env python3
"""
测试不同图片格式的支持
"""

import os
import requests
from PIL import Image
import io

def convert_image_format(input_path, output_path, format_name):
    """转换图片格式"""
    try:
        with Image.open(input_path) as img:
            # 确保是RGB模式
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 保存为指定格式
            img.save(output_path, format=format_name)
            print(f"✅ 成功转换: {output_path}")
            return True
    except Exception as e:
        print(f"❌ 转换失败 {output_path}: {e}")
        return False

def test_api_with_format(image_path, format_name):
    """测试API对特定格式的支持"""
    try:
        with open(image_path, 'rb') as f:
            response = requests.post(
                'http://localhost:8000/detect',
                files={'file': f}
            )
            result = response.json()
            
            if result.get('success'):
                print(f"✅ {format_name} 格式测试成功: 检测到 {result['face_count']} 个人脸")
                return True
            else:
                print(f"❌ {format_name} 格式测试失败: {result.get('message', '未知错误')}")
                return False
                
    except Exception as e:
        print(f"❌ {format_name} 格式测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 测试不同图片格式支持")
    print("=" * 50)
    
    # 检查原始测试图片
    source_image = "examples/obama.jpg"
    if not os.path.exists(source_image):
        print(f"❌ 源图片不存在: {source_image}")
        return
    
    # 创建测试目录
    test_dir = "format_test"
    os.makedirs(test_dir, exist_ok=True)
    
    # 要测试的格式
    formats = [
        ('JPEG', 'jpg'),
        ('PNG', 'png'),
        ('WebP', 'webp'),
        ('BMP', 'bmp'),
        ('GIF', 'gif')
    ]
    
    print("\n1. 转换图片格式:")
    converted_files = []
    
    for format_name, ext in formats:
        output_path = os.path.join(test_dir, f"test_image.{ext}")
        if convert_image_format(source_image, output_path, format_name):
            converted_files.append((output_path, format_name))
    
    print(f"\n2. 测试 API 支持:")
    
    # 检查API服务是否运行
    try:
        response = requests.get('http://localhost:8000/health')
        if response.status_code != 200:
            print("❌ API 服务未运行，请先启动服务器")
            return
    except:
        print("❌ 无法连接到 API 服务，请确保服务器正在运行")
        return
    
    # 测试每种格式
    success_count = 0
    for file_path, format_name in converted_files:
        if test_api_with_format(file_path, format_name):
            success_count += 1
    
    print(f"\n📊 测试结果:")
    print(f"总共测试: {len(converted_files)} 种格式")
    print(f"成功支持: {success_count} 种格式")
    print(f"成功率: {success_count/len(converted_files)*100:.1f}%")
    
    # 显示文件大小比较
    print(f"\n📁 文件大小比较:")
    for file_path, format_name in converted_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"{format_name:>6}: {size:>8,} bytes ({size/1024:.1f} KB)")
    
    # 清理测试文件
    cleanup = input("\n🗑️  是否删除测试文件? (y/N): ").strip().lower()
    if cleanup == 'y':
        import shutil
        shutil.rmtree(test_dir)
        print("✅ 测试文件已清理")
    else:
        print(f"📁 测试文件保留在: {test_dir}/")

def test_pillow_formats():
    """测试 Pillow 支持的格式"""
    print("\n🔍 Pillow 库支持的格式:")
    
    from PIL import Image, features
    
    # 检查特定格式支持
    formats_to_check = ['webp', 'jpeg', 'png', 'gif', 'bmp']
    
    for fmt in formats_to_check:
        supported = features.check(fmt)
        status = "✅" if supported else "❌"
        print(f"{status} {fmt.upper()}: {'支持' if supported else '不支持'}")
    
    # 显示所有支持的扩展名
    Image.init()
    supported_extensions = [ext for ext in Image.EXTENSION if Image.EXTENSION[ext] in Image.OPEN]
    print(f"\n📋 支持的文件扩展名 ({len(supported_extensions)} 种):")
    
    # 按类型分组显示
    image_exts = [ext for ext in supported_extensions if ext.lower() in ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff', '.tif']]
    other_exts = [ext for ext in supported_extensions if ext not in image_exts]
    
    print("常用图片格式:", ', '.join(sorted(image_exts)))
    if other_exts:
        print("其他格式:", ', '.join(sorted(other_exts[:10])) + ('...' if len(other_exts) > 10 else ''))

if __name__ == "__main__":
    test_pillow_formats()
    print()
    main()
