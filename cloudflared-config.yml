tunnel: face-recognition-api
credentials-file: /Users/<USER>/.cloudflared/7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d.json

ingress:
  # 人脸识别 API 服务 - 绑定到固定域名
  - hostname: face.juyingnj.com
    service: http://localhost:8000
    originRequest:
      # 优化设置
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s
  # 默认规则 (必须存在)
  - service: http_status:404

# 可选配置
logfile: /tmp/cloudflared.log
loglevel: info

# 连接优化
retries: 3
grace-period: 30s
