# Face Recognition FastAPI Web Service 使用指南

## 🎉 部署成功！

您的 Face Recognition FastAPI Web 服务已经成功部署并运行！

## 🚀 快速开始

### 1. 启动服务器

```bash
# 激活虚拟环境
source face_recognition_env/bin/activate

# 启动服务器
python start_api_server.py
```

服务器启动后，您可以访问：
- **主页**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs (Swagger UI)
- **ReDoc 文档**: http://localhost:8000/redoc

### 2. 测试 API

```bash
# 运行自动测试
python test_api_client.py

# 运行交互式测试
python test_api_client.py --interactive
```

## 📚 API 端点详解

### 🔍 人脸检测 `/detect`

检测图片中的人脸位置和数量

**请求方式**: POST
**参数**:
- `file`: 图片文件 (必需，支持 webp, png, jpg, jpeg, gif, bmp 格式)
- `model`: 检测模型 "hog" 或 "cnn" (可选，默认 "hog")
- `upsample`: 上采样次数 (可选，默认 1)

**示例**:
```bash
# 支持多种格式
curl -X POST "http://localhost:8000/detect" -F "file=@photo.webp"
curl -X POST "http://localhost:8000/detect" -F "file=@photo.png"
curl -X POST "http://localhost:8000/detect" -F "file=@photo.jpg"
```

**响应**:
```json
{
  "success": true,
  "message": "成功检测到 1 个人脸",
  "face_count": 1,
  "faces": [
    {
      "face_id": 1,
      "location": {"top": 241, "right": 740, "bottom": 562, "left": 419},
      "size": {"width": 321, "height": 321}
    }
  ],
  "processing_time": 0.456
}
```

### 👤 人脸识别 `/recognize`

识别图片中的人脸身份

**请求方式**: POST  
**参数**:
- `file`: 图片文件 (必需)
- `tolerance`: 识别容忍度 (可选，默认 0.6)
- `model`: 检测模型 (可选，默认 "hog")

**示例**:
```bash
curl -X POST "http://localhost:8000/recognize" \
  -F "file=@test.jpg" \
  -F "tolerance=0.6"
```

### 🔄 人脸比较 `/compare`

比较两张图片中的人脸是否为同一人

**请求方式**: POST  
**参数**:
- `file1`: 第一张图片 (必需)
- `file2`: 第二张图片 (必需)
- `tolerance`: 比较容忍度 (可选，默认 0.6)

**示例**:
```bash
curl -X POST "http://localhost:8000/compare" \
  -F "file1=@person1.jpg" \
  -F "file2=@person2.jpg"
```

### 📍 面部特征检测 `/landmarks`

检测人脸的详细特征点

**请求方式**: POST  
**参数**:
- `file`: 图片文件 (必需)
- `model`: 特征点模型 "large" (68点) 或 "small" (5点) (可选，默认 "large")

**示例**:
```bash
curl -X POST "http://localhost:8000/landmarks" \
  -F "file=@face.jpg" \
  -F "model=large"
```

## 🗄️ 数据库管理

### 📋 查看数据库 `/database`

**请求方式**: GET

```bash
curl "http://localhost:8000/database"
```

### ➕ 添加人员 `/database/add`

**请求方式**: POST  
**参数**:
- `file`: 人脸图片 (必需)
- `name`: 人员姓名 (必需)
- `description`: 人员描述 (可选)

```bash
curl -X POST "http://localhost:8000/database/add" \
  -F "file=@person.jpg" \
  -F "name=张三" \
  -F "description=员工"
```

### ❌ 删除人员 `/database/{name}`

**请求方式**: DELETE

```bash
curl -X DELETE "http://localhost:8000/database/张三"
```

### 🗑️ 清空数据库 `/database/clear`

**请求方式**: DELETE

```bash
curl -X DELETE "http://localhost:8000/database/clear"
```

## 🔧 批量处理

### 📦 批量人脸检测 `/batch/detect`

同时处理多张图片

**请求方式**: POST  
**参数**:
- `files`: 多个图片文件 (必需)
- `model`: 检测模型 (可选)

```bash
curl -X POST "http://localhost:8000/batch/detect" \
  -F "files=@photo1.jpg" \
  -F "files=@photo2.jpg" \
  -F "files=@photo3.jpg"
```

## 🐍 Python 客户端使用

### 安装依赖

```bash
pip install requests
```

### 使用示例

```python
import requests

# 人脸检测
with open('photo.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/detect',
        files={'file': f}
    )
    result = response.json()
    print(result)

# 添加人员
with open('person.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/database/add',
        files={'file': f},
        data={'name': '张三', 'description': '员工'}
    )
    result = response.json()
    print(result)

# 人脸识别
with open('test.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/recognize',
        files={'file': f}
    )
    result = response.json()
    print(result)
```

### 使用客户端类

```python
from test_api_client import FaceRecognitionClient

client = FaceRecognitionClient("http://localhost:8000")

# 健康检查
health = client.health_check()
print(health)

# 人脸检测
result = client.detect_faces("photo.jpg")
print(result)

# 添加人员
result = client.add_person("张三", "person.jpg", "员工")
print(result)

# 人脸识别
result = client.recognize_faces("test.jpg")
print(result)
```

## ⚙️ 配置说明

### 服务器配置

在 `face_recognition_api.py` 中可以修改：

```python
# 文件限制
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

# 数据库文件
FACE_DATABASE_FILE = "face_database.json"
```

### 启动参数

```python
# 修改 start_api_server.py 中的启动参数
uvicorn.run(
    "face_recognition_api:app",
    host="0.0.0.0",      # 监听地址
    port=8000,           # 端口
    reload=True,         # 自动重载
    log_level="info"     # 日志级别
)
```

## 🔒 安全建议

1. **生产环境部署**:
   - 使用 HTTPS
   - 添加身份验证
   - 限制文件上传大小
   - 添加速率限制

2. **文件验证**:
   - 验证文件类型
   - 检查文件内容
   - 限制文件大小

3. **错误处理**:
   - 不暴露敏感信息
   - 记录详细日志
   - 优雅处理异常

## 📊 性能优化

1. **模型选择**:
   - CPU 环境使用 HOG 模型
   - GPU 环境使用 CNN 模型

2. **批量处理**:
   - 使用批量 API 处理多张图片
   - 合理设置批次大小

3. **缓存策略**:
   - 缓存人脸编码
   - 使用 Redis 等缓存系统

## 🐛 故障排除

### 常见问题

1. **服务器启动失败**:
   ```bash
   # 检查端口是否被占用
   lsof -i :8000
   
   # 更换端口
   uvicorn.run(app, port=8001)
   ```

2. **图片处理失败**:
   - 检查图片格式是否支持
   - 确认图片文件完整
   - 检查文件大小限制

3. **人脸检测失败**:
   - 尝试不同的检测模型
   - 调整上采样参数
   - 检查图片质量

### 日志查看

服务器日志会显示详细的错误信息，帮助诊断问题。

## 🎯 实际应用场景

1. **门禁系统**: 员工人脸识别打卡
2. **安防监控**: 实时人脸识别报警
3. **相册管理**: 自动人脸分类整理
4. **身份验证**: 人脸比对验证身份
5. **考勤系统**: 学生/员工考勤管理

## 📈 扩展功能

可以基于此 API 开发：
- Web 前端界面
- 移动端 APP
- 微信小程序
- 桌面应用程序

---

🎊 **恭喜！您的 Face Recognition FastAPI Web 服务已经完全部署成功！**
