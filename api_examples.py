#!/usr/bin/env python3
"""
Face Recognition API 实用示例集合
"""

import face_recognition
import numpy as np
import os
from PIL import Image, ImageDraw, ImageFont

def example_1_basic_face_detection():
    """示例1: 基础人脸检测"""
    print("=== 示例1: 基础人脸检测 ===")
    
    # 加载图像
    image_path = "examples/biden.jpg"
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    image = face_recognition.load_image_file(image_path)
    print(f"图像尺寸: {image.shape}")
    
    # 检测人脸
    face_locations = face_recognition.face_locations(image)
    print(f"检测到 {len(face_locations)} 个人脸")
    
    # 打印人脸位置
    for i, (top, right, bottom, left) in enumerate(face_locations):
        print(f"人脸 {i+1}: 上={top}, 右={right}, 下={bottom}, 左={left}")
        print(f"人脸尺寸: {right-left} x {bottom-top} 像素")

def example_2_face_landmarks():
    """示例2: 面部特征检测"""
    print("\n=== 示例2: 面部特征检测 ===")
    
    image_path = "examples/biden.jpg"
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    image = face_recognition.load_image_file(image_path)
    
    # 检测面部特征
    face_landmarks_list = face_recognition.face_landmarks(image)
    print(f"检测到 {len(face_landmarks_list)} 个人脸的特征")
    
    for i, face_landmarks in enumerate(face_landmarks_list):
        print(f"\n人脸 {i+1} 的特征点:")
        for feature, points in face_landmarks.items():
            print(f"  {feature}: {len(points)} 个点")

def example_3_face_encoding():
    """示例3: 人脸编码生成"""
    print("\n=== 示例3: 人脸编码生成 ===")
    
    image_path = "examples/biden.jpg"
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    image = face_recognition.load_image_file(image_path)
    
    # 生成人脸编码
    face_encodings = face_recognition.face_encodings(image)
    print(f"生成了 {len(face_encodings)} 个人脸编码")
    
    if face_encodings:
        encoding = face_encodings[0]
        print(f"编码维度: {encoding.shape}")
        print(f"编码类型: {type(encoding)}")
        print(f"编码范围: [{encoding.min():.3f}, {encoding.max():.3f}]")

def example_4_face_comparison():
    """示例4: 人脸比较"""
    print("\n=== 示例4: 人脸比较 ===")
    
    # 检查文件是否存在
    biden_path = "examples/biden.jpg"
    obama_path = "examples/obama.jpg"
    
    if not os.path.exists(biden_path) or not os.path.exists(obama_path):
        print("需要的图像文件不存在")
        return
    
    # 加载图像
    biden_image = face_recognition.load_image_file(biden_path)
    obama_image = face_recognition.load_image_file(obama_path)
    
    # 生成编码
    biden_encodings = face_recognition.face_encodings(biden_image)
    obama_encodings = face_recognition.face_encodings(obama_image)
    
    if not biden_encodings or not obama_encodings:
        print("无法生成人脸编码")
        return
    
    biden_encoding = biden_encodings[0]
    obama_encoding = obama_encodings[0]
    
    # 比较人脸
    results = face_recognition.compare_faces([biden_encoding], obama_encoding)
    distance = face_recognition.face_distance([biden_encoding], obama_encoding)[0]
    
    print(f"Biden vs Obama:")
    print(f"  是否匹配: {results[0]}")
    print(f"  相似度距离: {distance:.3f}")
    print(f"  建议阈值: 0.6 (距离小于阈值认为是同一人)")

def example_5_multiple_faces():
    """示例5: 多人脸处理"""
    print("\n=== 示例5: 多人脸处理 ===")
    
    image_path = "examples/two_people.jpg"
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    image = face_recognition.load_image_file(image_path)
    
    # 检测所有人脸
    face_locations = face_recognition.face_locations(image)
    face_encodings = face_recognition.face_encodings(image, face_locations)
    
    print(f"检测到 {len(face_locations)} 个人脸")
    
    # 处理每个人脸
    for i, ((top, right, bottom, left), encoding) in enumerate(zip(face_locations, face_encodings)):
        print(f"\n人脸 {i+1}:")
        print(f"  位置: ({left}, {top}) 到 ({right}, {bottom})")
        print(f"  尺寸: {right-left} x {bottom-top} 像素")
        print(f"  编码维度: {encoding.shape}")

def example_6_tolerance_testing():
    """示例6: 容忍度测试"""
    print("\n=== 示例6: 容忍度测试 ===")
    
    biden_path = "examples/biden.jpg"
    obama_path = "examples/obama.jpg"
    
    if not os.path.exists(biden_path) or not os.path.exists(obama_path):
        print("需要的图像文件不存在")
        return
    
    # 加载和编码
    biden_image = face_recognition.load_image_file(biden_path)
    obama_image = face_recognition.load_image_file(obama_path)
    
    biden_encodings = face_recognition.face_encodings(biden_image)
    obama_encodings = face_recognition.face_encodings(obama_image)
    
    if not biden_encodings or not obama_encodings:
        print("无法生成人脸编码")
        return
    
    biden_encoding = biden_encodings[0]
    obama_encoding = obama_encodings[0]
    
    # 测试不同容忍度
    tolerances = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
    distance = face_recognition.face_distance([biden_encoding], obama_encoding)[0]
    
    print(f"Biden vs Obama 距离: {distance:.3f}")
    print("不同容忍度下的匹配结果:")
    
    for tolerance in tolerances:
        match = face_recognition.compare_faces([biden_encoding], obama_encoding, tolerance=tolerance)[0]
        print(f"  容忍度 {tolerance}: {'匹配' if match else '不匹配'}")

def example_7_model_comparison():
    """示例7: 模型比较"""
    print("\n=== 示例7: 检测模型比较 ===")
    
    image_path = "examples/biden.jpg"
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    image = face_recognition.load_image_file(image_path)
    
    # HOG模型检测
    import time
    start_time = time.time()
    hog_locations = face_recognition.face_locations(image, model="hog")
    hog_time = time.time() - start_time
    
    print(f"HOG模型:")
    print(f"  检测到 {len(hog_locations)} 个人脸")
    print(f"  耗时: {hog_time:.3f} 秒")
    
    # CNN模型检测（如果可用）
    try:
        start_time = time.time()
        cnn_locations = face_recognition.face_locations(image, model="cnn")
        cnn_time = time.time() - start_time
        
        print(f"\nCNN模型:")
        print(f"  检测到 {len(cnn_locations)} 个人脸")
        print(f"  耗时: {cnn_time:.3f} 秒")
        
        print(f"\n速度比较: CNN比HOG慢 {cnn_time/hog_time:.1f} 倍")
    except Exception as e:
        print(f"\nCNN模型不可用: {e}")

def create_face_database_example():
    """创建人脸数据库示例"""
    print("\n=== 创建人脸数据库示例 ===")
    
    # 模拟人脸数据库
    database = {}
    
    # 添加已知人脸
    known_people = ["biden.jpg", "obama.jpg"]
    
    for person_file in known_people:
        image_path = f"examples/{person_file}"
        if os.path.exists(image_path):
            name = person_file.split('.')[0].title()
            image = face_recognition.load_image_file(image_path)
            encodings = face_recognition.face_encodings(image)
            
            if encodings:
                database[name] = encodings[0]
                print(f"添加 {name} 到数据库")
    
    print(f"数据库包含 {len(database)} 个人")
    return database

def main():
    """运行所有示例"""
    print("Face Recognition API 示例演示")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not os.path.exists("examples"):
        print("请在项目根目录运行此脚本")
        return
    
    try:
        example_1_basic_face_detection()
        example_2_face_landmarks()
        example_3_face_encoding()
        example_4_face_comparison()
        example_5_multiple_faces()
        example_6_tolerance_testing()
        example_7_model_comparison()
        
        # 创建数据库示例
        database = create_face_database_example()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        print("请确保已正确安装face_recognition库")

if __name__ == "__main__":
    main()
