# Cloudflare Tunnel 快速部署指南

## 🚀 5分钟快速部署

### 前置条件
- ✅ 域名已托管在 Cloudflare
- ✅ 本地服务正在运行
- ✅ 网络连接正常

### 一键部署脚本

```bash
#!/bin/bash
# Cloudflare Tunnel 一键部署脚本

set -e

# 配置变量（请修改为您的实际值）
TUNNEL_NAME="your-service-name"
DOMAIN="your-domain.com"
LOCAL_SERVICE="http://localhost:8000"

echo "🚀 开始部署 Cloudflare Tunnel..."

# 1. 安装 cloudflared
if ! command -v cloudflared &> /dev/null; then
    echo "📦 安装 cloudflared..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install cloudflared
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64
        sudo mv cloudflared-linux-amd64 /usr/local/bin/cloudflared
        sudo chmod +x /usr/local/bin/cloudflared
    fi
fi

# 2. 登录认证（如果未认证）
if [ ! -f ~/.cloudflared/cert.pem ]; then
    echo "🔐 请完成 Cloudflare 登录认证..."
    cloudflared tunnel login
fi

# 3. 创建隧道
echo "🔧 创建隧道: $TUNNEL_NAME"
TUNNEL_ID=$(cloudflared tunnel create $TUNNEL_NAME | grep -o '[a-f0-9-]\{36\}')
echo "隧道 ID: $TUNNEL_ID"

# 4. 创建配置文件
echo "📝 创建配置文件..."
cat > cloudflared-config.yml << EOF
tunnel: $TUNNEL_NAME
credentials-file: ~/.cloudflared/$TUNNEL_ID.json

ingress:
  - hostname: $DOMAIN
    service: $LOCAL_SERVICE
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 10s
  - service: http_status:404

logfile: /tmp/cloudflared.log
loglevel: info
retries: 3
EOF

# 5. 配置 DNS
echo "🌐 配置 DNS 记录..."
cloudflared tunnel route dns $TUNNEL_NAME $DOMAIN || echo "⚠️  请手动添加 DNS 记录"

# 6. 创建启动脚本
cat > start-tunnel.sh << 'EOF'
#!/bin/bash
echo "🚀 启动 Cloudflare Tunnel..."
echo "🌐 域名: $DOMAIN"
echo "📡 本地服务: $LOCAL_SERVICE"
echo "❌ 按 Ctrl+C 停止"
echo "----------------------------------------"

if ! curl -s $LOCAL_SERVICE/health > /dev/null 2>&1; then
    echo "⚠️  本地服务未运行，请检查服务状态"
fi

cloudflared tunnel --config cloudflared-config.yml run $TUNNEL_NAME
EOF

chmod +x start-tunnel.sh

echo "✅ 部署完成！"
echo "🌐 访问地址: https://$DOMAIN"
echo "🚀 启动命令: ./start-tunnel.sh"
```

### 手动部署（3步骤）

#### 步骤 1：安装和认证
```bash
# 安装
brew install cloudflared  # macOS
# 或下载二进制文件

# 认证
cloudflared tunnel login
```

#### 步骤 2：创建隧道和配置
```bash
# 创建隧道
cloudflared tunnel create my-service

# 创建配置文件 cloudflared-config.yml
tunnel: my-service
credentials-file: ~/.cloudflared/[TUNNEL-ID].json
ingress:
  - hostname: my-domain.com
    service: http://localhost:8000
  - service: http_status:404
```

#### 步骤 3：启动服务
```bash
# 配置 DNS
cloudflared tunnel route dns my-service my-domain.com

# 启动隧道
cloudflared tunnel --config cloudflared-config.yml run my-service
```

## 🔧 常用命令速查

### 隧道管理
```bash
# 列出隧道
cloudflared tunnel list

# 查看隧道信息
cloudflared tunnel info TUNNEL_NAME

# 删除隧道
cloudflared tunnel delete TUNNEL_NAME

# 验证配置
cloudflared tunnel ingress validate
```

### 服务管理
```bash
# 启动隧道
cloudflared tunnel run TUNNEL_NAME

# 后台运行
nohup cloudflared tunnel run TUNNEL_NAME > tunnel.log 2>&1 &

# 停止隧道
pkill cloudflared
```

### 调试命令
```bash
# 详细日志
cloudflared tunnel --loglevel debug run TUNNEL_NAME

# 测试路由
cloudflared tunnel ingress rule https://your-domain.com

# 检查连接
curl -I https://your-domain.com
```

## 📋 配置模板

### 基础配置
```yaml
tunnel: service-name
credentials-file: ~/.cloudflared/tunnel-id.json
ingress:
  - hostname: domain.com
    service: http://localhost:8000
  - service: http_status:404
```

### 高级配置
```yaml
tunnel: service-name
credentials-file: ~/.cloudflared/tunnel-id.json

ingress:
  # 主服务
  - hostname: api.domain.com
    service: http://localhost:8000
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 10s
      keepAliveConnections: 10
  
  # 静态文件
  - hostname: static.domain.com
    service: http://localhost:3000
  
  # WebSocket 支持
  - hostname: ws.domain.com
    service: http://localhost:9000
    originRequest:
      noTLSVerify: true
  
  - service: http_status:404

# 全局设置
logfile: /var/log/cloudflared.log
loglevel: info
retries: 3
grace-period: 30s
```

### 多服务配置
```yaml
tunnel: multi-service
credentials-file: ~/.cloudflared/tunnel-id.json

ingress:
  # API 服务
  - hostname: api.domain.com
    service: http://localhost:8000
  
  # Web 界面
  - hostname: web.domain.com
    service: http://localhost:3000
  
  # 管理后台
  - hostname: admin.domain.com
    service: http://localhost:9000
  
  # 默认规则
  - service: http_status:404
```

## 🚨 故障排除速查

### 连接问题
```bash
# 检查隧道状态
cloudflared tunnel list

# 检查本地服务
curl http://localhost:8000/health

# 检查 DNS
nslookup your-domain.com
```

### 常见错误
| 错误信息 | 解决方案 |
|----------|----------|
| `Authentication error` | 重新运行 `cloudflared tunnel login` |
| `Tunnel not found` | 检查隧道名称和配置文件 |
| `Connection refused` | 确认本地服务正在运行 |
| `DNS resolution failed` | 检查 DNS 记录配置 |

### 性能优化
```yaml
# 在 originRequest 中添加
originRequest:
  connectTimeout: 10s
  tlsTimeout: 5s
  tcpKeepAlive: 30s
  keepAliveConnections: 100
  keepAliveTimeout: 90s
  httpHostHeader: your-domain.com
```

## 📞 支持联系

- 📖 完整文档：`Cloudflare_Tunnel_完整实施文档.md`
- 🔧 技术支持：[内部技术群]
- 📧 问题反馈：[技术邮箱]

---
**快速指南版本**：v1.0  
**适用场景**：快速部署和日常运维
