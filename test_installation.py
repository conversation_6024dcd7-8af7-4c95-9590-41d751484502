#!/usr/bin/env python3
"""
简单的face_recognition安装测试脚本
"""

print("正在测试face_recognition库的安装...")

try:
    import face_recognition
    print("✓ face_recognition库导入成功")
    
    # 测试基本功能
    import numpy as np
    from PIL import Image
    
    # 创建一个简单的测试图像
    test_image = np.zeros((100, 100, 3), dtype=np.uint8)
    print("✓ 创建测试图像成功")
    
    # 尝试检测人脸（应该返回空列表，因为是空白图像）
    face_locations = face_recognition.face_locations(test_image)
    print(f"✓ 人脸检测功能正常，检测到 {len(face_locations)} 个人脸")
    
    print("\n🎉 face_recognition库安装和基本功能测试成功！")
    print("\n可用的示例文件：")
    import os
    examples_dir = "examples"
    if os.path.exists(examples_dir):
        for file in os.listdir(examples_dir):
            if file.endswith('.py'):
                print(f"  - {file}")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
