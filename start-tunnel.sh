#!/bin/bash

# Face Recognition API Cloudflare Tunnel 启动脚本
# 绑定到固定域名: face.juyingnj.com

echo "🚀 启动 Face Recognition API Cloudflare Tunnel..."
echo "🌐 域名: face.juyingnj.com"
echo "📡 本地服务: http://localhost:8000"
echo "⚙️  配置文件: cloudflared-config.yml"
echo "❌ 按 Ctrl+C 停止隧道"
echo "-" | tr - - | head -c 50; echo

# 检查本地服务是否运行
if ! curl -s http://localhost:8000/health > /dev/null; then
    echo "⚠️  警告: 本地服务 (localhost:8000) 似乎没有运行"
    echo "请确保 Face Recognition API 服务已启动"
    echo ""
fi

# 启动隧道
cloudflared tunnel --config cloudflared-config.yml run face-recognition-api
