<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition API - Docker 部署版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            margin: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        .status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            transition: transform 0.2s, box-shadow 0.2s;
            font-weight: bold;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .btn.secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .feature h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .feature p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 Face Recognition API</h1>
        <p class="subtitle">基于 Docker 的人脸识别服务</p>
        
        <div class="status">
            <strong>🐳 Docker 部署状态:</strong> 
            <span id="status">检查中...</span>
        </div>
        
        <div class="buttons">
            <a href="web_test_page.html" class="btn">
                🧪 Web 测试界面
            </a>
            <a href="/docs" class="btn" target="_blank">
                📚 API 文档 (Swagger)
            </a>
            <a href="/redoc" class="btn" target="_blank">
                📖 API 文档 (ReDoc)
            </a>
            <a href="/health" class="btn secondary" target="_blank">
                ❤️ 健康检查
            </a>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🔍 人脸检测</h3>
                <p>检测图片中的人脸位置和数量，支持多种图片格式</p>
            </div>
            <div class="feature">
                <h3>👤 人脸识别</h3>
                <p>识别图片中的人脸身份，基于预建的人脸数据库</p>
            </div>
            <div class="feature">
                <h3>🔄 人脸比较</h3>
                <p>比较两张图片中的人脸是否为同一人</p>
            </div>
            <div class="feature">
                <h3>📍 特征检测</h3>
                <p>检测人脸的详细特征点，包括眼睛、鼻子、嘴巴等</p>
            </div>
            <div class="feature">
                <h3>🗄️ 数据库管理</h3>
                <p>添加、删除、查看人脸数据库中的人员信息</p>
            </div>
            <div class="feature">
                <h3>📦 批量处理</h3>
                <p>同时处理多张图片的人脸检测任务</p>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>支持的图片格式:</strong> WebP, PNG, JPEG, JPG, GIF, BMP</p>
            <p><strong>部署方式:</strong> Docker + Nginx + FastAPI</p>
            <p><strong>访问地址:</strong> <span id="currentUrl"></span></p>
        </div>
    </div>

    <script>
        // 显示当前 URL
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // 检查服务状态
        async function checkStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    document.getElementById('status').innerHTML = 
                        `<span style="color: #28a745;">✅ 服务正常运行</span> | 
                         数据库: ${data.database_size} 个人员 | 
                         时间: ${new Date(data.timestamp).toLocaleString()}`;
                } else {
                    document.getElementById('status').innerHTML = 
                        '<span style="color: #dc3545;">❌ 服务异常</span>';
                }
            } catch (error) {
                document.getElementById('status').innerHTML = 
                    '<span style="color: #dc3545;">❌ 无法连接到服务</span>';
            }
        }
        
        // 页面加载时检查状态
        checkStatus();
        
        // 每30秒检查一次状态
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>
