#!/usr/bin/env python3
"""
Face Recognition 高级API应用示例
包含实际应用场景的完整解决方案
"""

import face_recognition
import numpy as np
import os
import json
from PIL import Image, ImageDraw, ImageFont
import time

class FaceRecognitionSystem:
    """人脸识别系统类"""
    
    def __init__(self):
        self.known_face_encodings = []
        self.known_face_names = []
        self.tolerance = 0.6
        
    def add_person(self, name, image_path):
        """添加已知人员到系统"""
        try:
            image = face_recognition.load_image_file(image_path)
            encodings = face_recognition.face_encodings(image)
            
            if encodings:
                self.known_face_encodings.append(encodings[0])
                self.known_face_names.append(name)
                print(f"✓ 成功添加 {name}")
                return True
            else:
                print(f"✗ {name}: 未检测到人脸")
                return False
        except Exception as e:
            print(f"✗ {name}: 处理失败 - {e}")
            return False
    
    def identify_faces(self, image_path):
        """识别图像中的所有人脸"""
        try:
            image = face_recognition.load_image_file(image_path)
            face_locations = face_recognition.face_locations(image)
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            results = []
            for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
                matches = face_recognition.compare_faces(
                    self.known_face_encodings, face_encoding, tolerance=self.tolerance
                )
                name = "未知人员"
                confidence = 0.0
                
                if True in matches:
                    distances = face_recognition.face_distance(self.known_face_encodings, face_encoding)
                    best_match_index = np.argmin(distances)
                    
                    if matches[best_match_index]:
                        name = self.known_face_names[best_match_index]
                        confidence = 1 - distances[best_match_index]
                
                results.append({
                    'name': name,
                    'confidence': confidence,
                    'location': (top, right, bottom, left),
                    'box_size': (right - left, bottom - top)
                })
            
            return results
            
        except Exception as e:
            print(f"识别失败: {e}")
            return []
    
    def save_database(self, filename):
        """保存人脸数据库"""
        data = {
            'encodings': [encoding.tolist() for encoding in self.known_face_encodings],
            'names': self.known_face_names,
            'tolerance': self.tolerance
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据库已保存到 {filename}")
    
    def load_database(self, filename):
        """加载人脸数据库"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.known_face_encodings = [np.array(encoding) for encoding in data['encodings']]
            self.known_face_names = data['names']
            self.tolerance = data.get('tolerance', 0.6)
            
            print(f"数据库已从 {filename} 加载，包含 {len(self.known_face_names)} 个人员")
            return True
        except Exception as e:
            print(f"加载数据库失败: {e}")
            return False

def demo_face_recognition_system():
    """演示人脸识别系统"""
    print("=== 人脸识别系统演示 ===")
    
    # 创建系统实例
    system = FaceRecognitionSystem()
    
    # 添加已知人员
    print("\n1. 构建人脸数据库:")
    known_people = [
        ("拜登", "examples/biden.jpg"),
        ("奥巴马", "examples/obama.jpg")
    ]
    
    for name, image_path in known_people:
        if os.path.exists(image_path):
            system.add_person(name, image_path)
    
    # 保存数据库
    system.save_database("face_database.json")
    
    # 识别测试
    print("\n2. 人脸识别测试:")
    test_images = ["examples/biden.jpg", "examples/obama.jpg", "examples/two_people.jpg"]
    
    for test_image in test_images:
        if os.path.exists(test_image):
            print(f"\n测试图像: {test_image}")
            results = system.identify_faces(test_image)
            
            for i, result in enumerate(results):
                print(f"  人脸 {i+1}: {result['name']} (置信度: {result['confidence']:.3f})")
                print(f"    位置: {result['location']}")
                print(f"    尺寸: {result['box_size'][0]}x{result['box_size'][1]} 像素")

def demo_face_comparison_matrix():
    """演示人脸比较矩阵"""
    print("\n=== 人脸相似度矩阵演示 ===")
    
    # 收集所有可用图像
    image_files = []
    names = []
    
    example_images = ["biden.jpg", "obama.jpg"]
    for img_file in example_images:
        img_path = f"examples/{img_file}"
        if os.path.exists(img_path):
            image_files.append(img_path)
            names.append(img_file.split('.')[0].title())
    
    if len(image_files) < 2:
        print("需要至少2张图像进行比较")
        return
    
    # 生成所有人脸编码
    encodings = []
    for img_path in image_files:
        image = face_recognition.load_image_file(img_path)
        face_encodings = face_recognition.face_encodings(image)
        if face_encodings:
            encodings.append(face_encodings[0])
        else:
            encodings.append(None)
    
    # 创建相似度矩阵
    print(f"\n相似度矩阵 (距离值，越小越相似):")
    print("     ", end="")
    for name in names:
        print(f"{name:>8}", end="")
    print()
    
    for i, name1 in enumerate(names):
        print(f"{name1:>5}", end="")
        for j, name2 in enumerate(names):
            if encodings[i] is not None and encodings[j] is not None:
                distance = face_recognition.face_distance([encodings[i]], encodings[j])[0]
                print(f"{distance:>8.3f}", end="")
            else:
                print(f"{'N/A':>8}", end="")
        print()

def demo_face_landmarks_analysis():
    """演示面部特征分析"""
    print("\n=== 面部特征分析演示 ===")
    
    image_path = "examples/biden.jpg"
    if not os.path.exists(image_path):
        print("测试图像不存在")
        return
    
    image = face_recognition.load_image_file(image_path)
    face_landmarks_list = face_recognition.face_landmarks(image)
    
    if not face_landmarks_list:
        print("未检测到面部特征")
        return
    
    landmarks = face_landmarks_list[0]
    
    print("面部特征统计:")
    
    # 计算面部特征的几何信息
    def calculate_distance(point1, point2):
        return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    # 眼睛间距
    left_eye_center = np.mean(landmarks['left_eye'], axis=0)
    right_eye_center = np.mean(landmarks['right_eye'], axis=0)
    eye_distance = calculate_distance(left_eye_center, right_eye_center)
    
    # 鼻子长度
    nose_top = landmarks['nose_bridge'][0]
    nose_bottom = landmarks['nose_tip'][2]  # 鼻尖中心点
    nose_length = calculate_distance(nose_top, nose_bottom)
    
    # 嘴巴宽度
    mouth_left = landmarks['top_lip'][0]
    mouth_right = landmarks['top_lip'][6]
    mouth_width = calculate_distance(mouth_left, mouth_right)
    
    print(f"  眼睛间距: {eye_distance:.1f} 像素")
    print(f"  鼻子长度: {nose_length:.1f} 像素")
    print(f"  嘴巴宽度: {mouth_width:.1f} 像素")
    print(f"  面部比例 (眼距:鼻长:嘴宽) = {eye_distance/eye_distance:.1f}:{nose_length/eye_distance:.1f}:{mouth_width/eye_distance:.1f}")

def demo_performance_benchmark():
    """演示性能基准测试"""
    print("\n=== 性能基准测试 ===")
    
    image_path = "examples/biden.jpg"
    if not os.path.exists(image_path):
        print("测试图像不存在")
        return
    
    image = face_recognition.load_image_file(image_path)
    
    # 测试不同参数的性能
    test_configs = [
        {"model": "hog", "upsample": 1, "jitters": 1},
        {"model": "hog", "upsample": 2, "jitters": 1},
        {"model": "hog", "upsample": 1, "jitters": 10},
        {"model": "cnn", "upsample": 1, "jitters": 1},
    ]
    
    print("配置                    | 检测时间 | 编码时间 | 总时间   | 检测数")
    print("-" * 65)
    
    for config in test_configs:
        try:
            # 人脸检测
            start_time = time.time()
            face_locations = face_recognition.face_locations(
                image, 
                number_of_times_to_upsample=config["upsample"],
                model=config["model"]
            )
            detection_time = time.time() - start_time
            
            # 人脸编码
            start_time = time.time()
            face_encodings = face_recognition.face_encodings(
                image, 
                known_face_locations=face_locations,
                num_jitters=config["jitters"]
            )
            encoding_time = time.time() - start_time
            
            total_time = detection_time + encoding_time
            
            config_str = f"{config['model']}/up{config['upsample']}/j{config['jitters']}"
            print(f"{config_str:<23} | {detection_time:>6.3f}s | {encoding_time:>6.3f}s | {total_time:>6.3f}s | {len(face_locations):>5d}")
            
        except Exception as e:
            config_str = f"{config['model']}/up{config['upsample']}/j{config['jitters']}"
            print(f"{config_str:<23} | 错误: {str(e)[:30]}")

def demo_batch_processing():
    """演示批量处理"""
    print("\n=== 批量处理演示 ===")
    
    # 收集所有可用的示例图像
    example_dir = "examples"
    image_files = []
    
    for filename in os.listdir(example_dir):
        if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
            image_files.append(os.path.join(example_dir, filename))
    
    if len(image_files) < 2:
        print("需要至少2张图像进行批量处理演示")
        return
    
    print(f"找到 {len(image_files)} 张图像")
    
    # 批量加载图像
    images = []
    valid_files = []
    
    for img_path in image_files[:5]:  # 限制处理数量
        try:
            image = face_recognition.load_image_file(img_path)
            images.append(image)
            valid_files.append(img_path)
        except Exception as e:
            print(f"跳过 {img_path}: {e}")
    
    if not images:
        print("没有有效的图像可处理")
        return
    
    print(f"成功加载 {len(images)} 张图像")
    
    # 批量人脸检测（仅CNN模型支持）
    try:
        start_time = time.time()
        batch_locations = face_recognition.batch_face_locations(images, batch_size=len(images))
        batch_time = time.time() - start_time
        
        print(f"批量检测耗时: {batch_time:.3f}秒")
        
        total_faces = sum(len(locations) for locations in batch_locations)
        print(f"总共检测到 {total_faces} 个人脸")
        
        for i, (img_path, locations) in enumerate(zip(valid_files, batch_locations)):
            filename = os.path.basename(img_path)
            print(f"  {filename}: {len(locations)} 个人脸")
            
    except Exception as e:
        print(f"批量处理失败: {e}")
        print("注意: 批量处理需要CNN模型支持")

def main():
    """运行所有高级示例"""
    print("Face Recognition 高级API应用示例")
    print("=" * 60)
    
    # 检查环境
    if not os.path.exists("examples"):
        print("请在项目根目录运行此脚本")
        return
    
    try:
        # 运行各种演示
        demo_face_recognition_system()
        demo_face_comparison_matrix()
        demo_face_landmarks_analysis()
        demo_performance_benchmark()
        demo_batch_processing()
        
        print("\n" + "=" * 60)
        print("所有高级示例演示完成！")
        print("\n💡 提示:")
        print("- 调整tolerance参数可以改变识别严格程度")
        print("- CNN模型更准确但需要更多计算资源")
        print("- 增加num_jitters可以提高编码精度但会变慢")
        print("- 批量处理适合处理大量图像")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")

if __name__ == "__main__":
    main()
