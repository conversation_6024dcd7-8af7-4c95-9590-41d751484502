# Cloudflare Tunnel 固定域名设置指南

## 🎯 目标
将人脸识别 API 服务绑定到固定域名：`face.juyingnj.com`

## ✅ 已完成的步骤

1. **创建命名隧道**: `face-recognition-api`
   - 隧道 ID: `7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d`
   - 凭证文件: `/Users/<USER>/.cloudflared/7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d.json`

2. **配置文件**: `cloudflared-config.yml` ✅
3. **启动脚本**: `start-tunnel.sh` ✅

## 🔧 需要手动完成的步骤

### 步骤 1: 在 Cloudflare 控制台添加 DNS 记录

1. 访问：https://dash.cloudflare.com
2. 选择域名：`juyingnj.com`
3. 进入 **DNS** 选项卡
4. 点击 **添加记录**
5. 设置如下：
   ```
   类型: CNAME
   名称: face
   目标: 7b1b7bb1-e0b7-4f6e-8da8-6c85706e441d.cfargotunnel.com
   代理状态: 已代理 (橙色云朵)
   TTL: 自动
   ```
6. 点击 **保存**

### 步骤 2: 启动隧道服务

```bash
# 方法 1: 使用启动脚本
./start-tunnel.sh

# 方法 2: 直接命令
cloudflared tunnel --config cloudflared-config.yml run face-recognition-api
```

## 🌐 访问地址

设置完成后，您的服务将通过以下地址访问：

- **主页**: https://face.juyingnj.com
- **API 文档**: https://face.juyingnj.com/docs
- **健康检查**: https://face.juyingnj.com/health
- **人脸检测**: https://face.juyingnj.com/detect
- **人脸识别**: https://face.juyingnj.com/recognize

## 🔍 验证步骤

1. **检查 DNS 解析**:
   ```bash
   nslookup face.juyingnj.com
   ```

2. **测试服务**:
   ```bash
   curl https://face.juyingnj.com/health
   ```

## 📝 注意事项

- DNS 记录生效可能需要几分钟时间
- 确保本地服务 (localhost:8000) 正在运行
- 隧道需要保持运行状态才能访问服务
- 如需生产环境部署，建议设置为系统服务

## 🚀 生产环境部署 (可选)

如需设置为系统服务，可以创建 systemd 服务文件或使用 launchd (macOS)。
