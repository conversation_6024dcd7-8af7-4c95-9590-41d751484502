# Face Recognition API 快速参考

## 🚀 快速开始

```python
import face_recognition

# 基本流程
image = face_recognition.load_image_file("photo.jpg")
face_locations = face_recognition.face_locations(image)
face_encodings = face_recognition.face_encodings(image, face_locations)
```

## 📋 核心函数速查

### 图像加载
```python
# 加载图像文件
image = face_recognition.load_image_file("photo.jpg")
image = face_recognition.load_image_file("photo.jpg", mode="L")  # 黑白模式
```

### 人脸检测
```python
# HOG模型（快速）
locations = face_recognition.face_locations(image)
locations = face_recognition.face_locations(image, number_of_times_to_upsample=2)

# CNN模型（准确）
locations = face_recognition.face_locations(image, model="cnn")

# 批量检测（GPU）
batch_locations = face_recognition.batch_face_locations(images, batch_size=32)
```

### 面部特征
```python
# 68个特征点
landmarks = face_recognition.face_landmarks(image)

# 5个特征点（快速）
landmarks = face_recognition.face_landmarks(image, model="small")

# 指定人脸位置
landmarks = face_recognition.face_landmarks(image, face_locations)
```

### 人脸编码
```python
# 基本编码
encodings = face_recognition.face_encodings(image)

# 高精度编码
encodings = face_recognition.face_encodings(image, num_jitters=10)

# 指定人脸位置
encodings = face_recognition.face_encodings(image, known_face_locations=locations)
```

### 人脸比较
```python
# 比较人脸
matches = face_recognition.compare_faces([known_encoding], unknown_encoding)
matches = face_recognition.compare_faces([known_encoding], unknown_encoding, tolerance=0.5)

# 计算距离
distances = face_recognition.face_distance([known_encoding], unknown_encoding)
```

## ⚙️ 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `model` | "hog" | 检测模型："hog"(快) 或 "cnn"(准) |
| `number_of_times_to_upsample` | 1 | 上采样次数，越大检测越精确 |
| `tolerance` | 0.6 | 容忍度，越小越严格 |
| `num_jitters` | 1 | 编码重采样次数，越大越准确 |
| `batch_size` | 128 | 批处理大小 |

## 🎯 常用代码片段

### 1. 简单人脸识别
```python
def simple_face_recognition(known_image_path, test_image_path):
    # 加载已知人脸
    known_image = face_recognition.load_image_file(known_image_path)
    known_encoding = face_recognition.face_encodings(known_image)[0]
    
    # 加载测试图像
    test_image = face_recognition.load_image_file(test_image_path)
    test_encodings = face_recognition.face_encodings(test_image)
    
    # 比较人脸
    for encoding in test_encodings:
        matches = face_recognition.compare_faces([known_encoding], encoding)
        if matches[0]:
            return "匹配"
    return "不匹配"
```

### 2. 多人识别系统
```python
class FaceRecognizer:
    def __init__(self):
        self.known_encodings = []
        self.known_names = []
    
    def add_person(self, name, image_path):
        image = face_recognition.load_image_file(image_path)
        encoding = face_recognition.face_encodings(image)[0]
        self.known_encodings.append(encoding)
        self.known_names.append(name)
    
    def recognize(self, image_path):
        image = face_recognition.load_image_file(image_path)
        face_encodings = face_recognition.face_encodings(image)
        
        results = []
        for encoding in face_encodings:
            matches = face_recognition.compare_faces(self.known_encodings, encoding)
            name = "未知"
            if True in matches:
                match_index = matches.index(True)
                name = self.known_names[match_index]
            results.append(name)
        return results
```

### 3. 人脸特征分析
```python
def analyze_face_features(image_path):
    image = face_recognition.load_image_file(image_path)
    landmarks = face_recognition.face_landmarks(image)[0]
    
    # 计算特征
    left_eye = np.mean(landmarks['left_eye'], axis=0)
    right_eye = np.mean(landmarks['right_eye'], axis=0)
    eye_distance = np.linalg.norm(left_eye - right_eye)
    
    return {
        'eye_distance': eye_distance,
        'landmarks': landmarks
    }
```

### 4. 批量处理
```python
def batch_process_images(image_paths):
    results = []
    for path in image_paths:
        try:
            image = face_recognition.load_image_file(path)
            locations = face_recognition.face_locations(image)
            encodings = face_recognition.face_encodings(image, locations)
            
            results.append({
                'path': path,
                'face_count': len(locations),
                'encodings': encodings
            })
        except Exception as e:
            results.append({'path': path, 'error': str(e)})
    
    return results
```

## 🔧 性能优化技巧

### 1. 选择合适的模型
```python
# CPU环境 - 使用HOG
locations = face_recognition.face_locations(image, model="hog")

# GPU环境 - 使用CNN
locations = face_recognition.face_locations(image, model="cnn")
```

### 2. 预先检测人脸位置
```python
# 一次检测，多次使用
face_locations = face_recognition.face_locations(image)
encodings = face_recognition.face_encodings(image, face_locations)
landmarks = face_recognition.face_landmarks(image, face_locations)
```

### 3. 调整精度参数
```python
# 快速模式
encodings = face_recognition.face_encodings(image, num_jitters=1)

# 高精度模式
encodings = face_recognition.face_encodings(image, num_jitters=10)
```

### 4. 批量处理
```python
# 处理多张相同尺寸的图像
batch_locations = face_recognition.batch_face_locations(images)
```

## ⚠️ 常见问题

### 1. 未检测到人脸
```python
face_locations = face_recognition.face_locations(image)
if not face_locations:
    # 尝试增加上采样次数
    face_locations = face_recognition.face_locations(image, number_of_times_to_upsample=2)
```

### 2. 识别精度不够
```python
# 降低容忍度
matches = face_recognition.compare_faces(known_encodings, unknown_encoding, tolerance=0.5)

# 增加编码精度
encodings = face_recognition.face_encodings(image, num_jitters=10)
```

### 3. 处理速度慢
```python
# 使用HOG模型
locations = face_recognition.face_locations(image, model="hog")

# 减少上采样
locations = face_recognition.face_locations(image, number_of_times_to_upsample=1)
```

## 📊 返回值格式

### face_locations 返回格式
```python
# [(top, right, bottom, left), ...]
[(241, 740, 562, 419)]
```

### face_landmarks 返回格式
```python
# [{'feature_name': [(x, y), ...], ...}, ...]
[{
    'chin': [(x1, y1), (x2, y2), ...],
    'left_eye': [(x1, y1), ...],
    'right_eye': [(x1, y1), ...],
    # ... 其他特征
}]
```

### face_encodings 返回格式
```python
# [numpy.ndarray(128,), ...]
[array([-0.1, 0.2, 0.05, ...])]  # 128维向量
```

### compare_faces 返回格式
```python
# [True/False, ...]
[True, False, True]
```

### face_distance 返回格式
```python
# numpy.ndarray
array([0.3, 0.8, 0.2])  # 距离值，越小越相似
```

## 🎨 实用工具函数

### 绘制人脸框
```python
from PIL import Image, ImageDraw

def draw_face_boxes(image_path, output_path):
    image = face_recognition.load_image_file(image_path)
    face_locations = face_recognition.face_locations(image)
    
    pil_image = Image.fromarray(image)
    draw = ImageDraw.Draw(pil_image)
    
    for (top, right, bottom, left) in face_locations:
        draw.rectangle([(left, top), (right, bottom)], outline="red", width=3)
    
    pil_image.save(output_path)
```

### 计算人脸相似度
```python
def calculate_similarity(encoding1, encoding2):
    distance = face_recognition.face_distance([encoding1], encoding2)[0]
    similarity = 1 - distance  # 转换为相似度
    return max(0, similarity)  # 确保非负
```
