#!/usr/bin/env python3
"""
Face Recognition API 客户端测试脚本
"""

import requests
import json
import os
from typing import Optional

class FaceRecognitionClient:
    """Face Recognition API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        
    def health_check(self):
        """健康检查"""
        try:
            response = requests.get(f"{self.base_url}/health")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def detect_faces(self, image_path: str, model: str = "hog", upsample: int = 1):
        """人脸检测"""
        try:
            with open(image_path, 'rb') as f:
                files = {'file': f}
                params = {'model': model, 'upsample': upsample}
                response = requests.post(f"{self.base_url}/detect", files=files, params=params)
                return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def recognize_faces(self, image_path: str, tolerance: float = 0.6, model: str = "hog"):
        """人脸识别"""
        try:
            with open(image_path, 'rb') as f:
                files = {'file': f}
                params = {'tolerance': tolerance, 'model': model}
                response = requests.post(f"{self.base_url}/recognize", files=files, params=params)
                return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def compare_faces(self, image1_path: str, image2_path: str, tolerance: float = 0.6):
        """人脸比较"""
        try:
            with open(image1_path, 'rb') as f1, open(image2_path, 'rb') as f2:
                files = {'file1': f1, 'file2': f2}
                params = {'tolerance': tolerance}
                response = requests.post(f"{self.base_url}/compare", files=files, params=params)
                return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def detect_landmarks(self, image_path: str, model: str = "large"):
        """面部特征检测"""
        try:
            with open(image_path, 'rb') as f:
                files = {'file': f}
                params = {'model': model}
                response = requests.post(f"{self.base_url}/landmarks", files=files, params=params)
                return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def add_person(self, name: str, image_path: str, description: str = ""):
        """添加人员到数据库"""
        try:
            with open(image_path, 'rb') as f:
                files = {'file': f}
                data = {'name': name, 'description': description}
                response = requests.post(f"{self.base_url}/database/add", files=files, data=data)
                return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def get_database(self):
        """查看数据库"""
        try:
            response = requests.get(f"{self.base_url}/database")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def delete_person(self, name: str):
        """删除人员"""
        try:
            response = requests.delete(f"{self.base_url}/database/{name}")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def clear_database(self):
        """清空数据库"""
        try:
            response = requests.delete(f"{self.base_url}/database/clear")
            return response.json()
        except Exception as e:
            return {"error": str(e)}

def test_api():
    """测试 API 功能"""
    client = FaceRecognitionClient()
    
    print("🧪 Face Recognition API 测试")
    print("=" * 50)
    
    # 健康检查
    print("\n1. 健康检查:")
    health = client.health_check()
    print(json.dumps(health, indent=2, ensure_ascii=False))
    
    # 检查测试图片是否存在
    test_images = ["examples/biden.jpg", "examples/obama.jpg"]
    available_images = [img for img in test_images if os.path.exists(img)]
    
    if not available_images:
        print("\n❌ 未找到测试图片，请确保在项目根目录运行")
        return
    
    # 人脸检测测试
    print(f"\n2. 人脸检测测试 ({available_images[0]}):")
    detect_result = client.detect_faces(available_images[0])
    print(json.dumps(detect_result, indent=2, ensure_ascii=False))
    
    # 添加人员到数据库
    if len(available_images) >= 2:
        print(f"\n3. 添加人员到数据库:")
        add_result1 = client.add_person("拜登", "examples/biden.jpg", "美国前总统")
        print(json.dumps(add_result1, indent=2, ensure_ascii=False))
        
        add_result2 = client.add_person("奥巴马", "examples/obama.jpg", "美国前总统")
        print(json.dumps(add_result2, indent=2, ensure_ascii=False))
        
        # 查看数据库
        print(f"\n4. 查看数据库:")
        db_result = client.get_database()
        print(json.dumps(db_result, indent=2, ensure_ascii=False))
        
        # 人脸识别测试
        print(f"\n5. 人脸识别测试:")
        recognize_result = client.recognize_faces(available_images[0])
        print(json.dumps(recognize_result, indent=2, ensure_ascii=False))
        
        # 人脸比较测试
        print(f"\n6. 人脸比较测试:")
        compare_result = client.compare_faces(available_images[0], available_images[1])
        print(json.dumps(compare_result, indent=2, ensure_ascii=False))
    
    # 面部特征检测测试
    print(f"\n7. 面部特征检测测试:")
    landmarks_result = client.detect_landmarks(available_images[0])
    if landmarks_result.get("success"):
        # 只显示特征点数量，不显示具体坐标（太多了）
        landmarks_summary = {
            "success": landmarks_result["success"],
            "message": landmarks_result["message"],
            "face_count": len(landmarks_result["landmarks"]),
            "features_per_face": {}
        }
        if landmarks_result["landmarks"]:
            for feature, points in landmarks_result["landmarks"][0]["features"].items():
                landmarks_summary["features_per_face"][feature] = len(points)
        print(json.dumps(landmarks_summary, indent=2, ensure_ascii=False))
    else:
        print(json.dumps(landmarks_result, indent=2, ensure_ascii=False))
    
    print("\n✅ API 测试完成!")

def interactive_test():
    """交互式测试"""
    client = FaceRecognitionClient()
    
    while True:
        print("\n" + "=" * 50)
        print("🎮 Face Recognition API 交互式测试")
        print("1. 健康检查")
        print("2. 人脸检测")
        print("3. 人脸识别")
        print("4. 人脸比较")
        print("5. 面部特征检测")
        print("6. 添加人员")
        print("7. 查看数据库")
        print("8. 删除人员")
        print("9. 清空数据库")
        print("0. 退出")
        
        choice = input("\n请选择功能 (0-9): ").strip()
        
        if choice == "0":
            print("👋 再见!")
            break
        elif choice == "1":
            result = client.health_check()
            print(json.dumps(result, indent=2, ensure_ascii=False))
        elif choice == "2":
            image_path = input("请输入图片路径: ").strip()
            if os.path.exists(image_path):
                result = client.detect_faces(image_path)
                print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                print("❌ 文件不存在")
        elif choice == "3":
            image_path = input("请输入图片路径: ").strip()
            if os.path.exists(image_path):
                result = client.recognize_faces(image_path)
                print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                print("❌ 文件不存在")
        elif choice == "6":
            name = input("请输入人员姓名: ").strip()
            image_path = input("请输入图片路径: ").strip()
            description = input("请输入描述 (可选): ").strip()
            if os.path.exists(image_path):
                result = client.add_person(name, image_path, description)
                print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                print("❌ 文件不存在")
        elif choice == "7":
            result = client.get_database()
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("❌ 无效选择")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        test_api()

if __name__ == "__main__":
    main()
