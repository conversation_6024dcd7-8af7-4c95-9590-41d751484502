# Face Recognition API 详细使用指南

## 📚 核心 API 函数

### 1. 图像加载

#### `load_image_file(file, mode='RGB')`
加载图像文件到 numpy 数组

**参数：**
- `file`: 图像文件路径或文件对象
- `mode`: 图像格式，支持 'RGB'（彩色）和 'L'（黑白）

**返回：** numpy 数组格式的图像

```python
import face_recognition

# 加载图像
image = face_recognition.load_image_file("photo.jpg")
print(f"图像尺寸: {image.shape}")  # (高度, 宽度, 通道数)
```

### 2. 人脸检测

#### `face_locations(img, number_of_times_to_upsample=1, model="hog")`
检测图像中的人脸位置

**参数：**
- `img`: numpy 数组格式的图像
- `number_of_times_to_upsample`: 上采样次数，数值越高检测越精确但速度越慢
- `model`: 检测模型
  - `"hog"`: HOG模型（默认），CPU友好，速度快
  - `"cnn"`: CNN模型，更准确但需要GPU加速

**返回：** 人脸位置列表，格式为 (top, right, bottom, left)

```python
# 基础人脸检测
face_locations = face_recognition.face_locations(image)
print(f"检测到 {len(face_locations)} 个人脸")

# 使用CNN模型（更准确）
face_locations_cnn = face_recognition.face_locations(image, model="cnn")

# 提高检测精度（检测更小的人脸）
face_locations_precise = face_recognition.face_locations(image, number_of_times_to_upsample=2)
```

#### `batch_face_locations(images, number_of_times_to_upsample=1, batch_size=128)`
批量检测多张图像中的人脸（GPU加速）

**参数：**
- `images`: 图像列表
- `number_of_times_to_upsample`: 上采样次数
- `batch_size`: 批处理大小

```python
# 批量处理（适用于GPU环境）
images = [face_recognition.load_image_file(f"photo_{i}.jpg") for i in range(10)]
batch_locations = face_recognition.batch_face_locations(images, batch_size=32)
```

### 3. 面部特征检测

#### `face_landmarks(face_image, face_locations=None, model="large")`
检测面部特征点（眼睛、鼻子、嘴巴等）

**参数：**
- `face_image`: 图像数组
- `face_locations`: 可选，已知的人脸位置
- `model`: 模型类型
  - `"large"`: 68个特征点（默认）
  - `"small"`: 5个特征点（更快）

**返回：** 特征点字典列表

```python
# 检测面部特征
face_landmarks_list = face_recognition.face_landmarks(image)

for face_landmarks in face_landmarks_list:
    # 68点模型包含的特征
    print("下巴:", face_landmarks['chin'])
    print("左眉毛:", face_landmarks['left_eyebrow'])
    print("右眉毛:", face_landmarks['right_eyebrow'])
    print("鼻梁:", face_landmarks['nose_bridge'])
    print("鼻尖:", face_landmarks['nose_tip'])
    print("左眼:", face_landmarks['left_eye'])
    print("右眼:", face_landmarks['right_eye'])
    print("上唇:", face_landmarks['top_lip'])
    print("下唇:", face_landmarks['bottom_lip'])

# 使用5点模型（更快）
face_landmarks_small = face_recognition.face_landmarks(image, model="small")
```

### 4. 人脸编码

#### `face_encodings(face_image, known_face_locations=None, num_jitters=1, model="small")`
生成人脸的128维特征向量

**参数：**
- `face_image`: 图像数组
- `known_face_locations`: 可选，已知人脸位置
- `num_jitters`: 重采样次数，越高越准确但越慢
- `model`: 特征点模型，"large" 或 "small"

**返回：** 128维特征向量列表

```python
# 生成人脸编码
face_encodings = face_recognition.face_encodings(image)

# 提高编码精度
face_encodings_precise = face_recognition.face_encodings(image, num_jitters=10)

# 如果已知人脸位置，可以加速处理
face_locations = face_recognition.face_locations(image)
face_encodings_fast = face_recognition.face_encodings(image, known_face_locations=face_locations)
```

### 5. 人脸比较

#### `compare_faces(known_face_encodings, face_encoding_to_check, tolerance=0.6)`
比较人脸编码，判断是否为同一人

**参数：**
- `known_face_encodings`: 已知人脸编码列表
- `face_encoding_to_check`: 待比较的人脸编码
- `tolerance`: 容忍度，越小越严格（默认0.6）

**返回：** 布尔值列表，表示是否匹配

```python
# 人脸识别示例
known_image = face_recognition.load_image_file("known_person.jpg")
unknown_image = face_recognition.load_image_file("unknown_person.jpg")

# 生成编码
known_encoding = face_recognition.face_encodings(known_image)[0]
unknown_encoding = face_recognition.face_encodings(unknown_image)[0]

# 比较人脸
results = face_recognition.compare_faces([known_encoding], unknown_encoding)
print(f"是同一人: {results[0]}")

# 调整容忍度
results_strict = face_recognition.compare_faces([known_encoding], unknown_encoding, tolerance=0.5)
results_loose = face_recognition.compare_faces([known_encoding], unknown_encoding, tolerance=0.7)
```

#### `face_distance(face_encodings, face_to_compare)`
计算人脸编码之间的欧几里得距离

**参数：**
- `face_encodings`: 人脸编码列表
- `face_to_compare`: 待比较的人脸编码

**返回：** 距离数组，数值越小越相似

```python
# 计算人脸距离
distances = face_recognition.face_distance([known_encoding], unknown_encoding)
print(f"人脸距离: {distances[0]}")

# 距离越小，相似度越高
if distances[0] < 0.6:
    print("很可能是同一人")
else:
    print("可能不是同一人")
```

## 🔧 实用示例

### 完整的人脸识别流程

```python
import face_recognition
import numpy as np

def recognize_face(known_image_path, unknown_image_path):
    """完整的人脸识别流程"""
    
    # 1. 加载图像
    known_image = face_recognition.load_image_file(known_image_path)
    unknown_image = face_recognition.load_image_file(unknown_image_path)
    
    # 2. 检测人脸
    known_face_locations = face_recognition.face_locations(known_image)
    unknown_face_locations = face_recognition.face_locations(unknown_image)
    
    if len(known_face_locations) == 0:
        return "已知图像中未检测到人脸"
    if len(unknown_face_locations) == 0:
        return "未知图像中未检测到人脸"
    
    # 3. 生成人脸编码
    known_face_encodings = face_recognition.face_encodings(known_image, known_face_locations)
    unknown_face_encodings = face_recognition.face_encodings(unknown_image, unknown_face_locations)
    
    # 4. 比较人脸
    for unknown_encoding in unknown_face_encodings:
        matches = face_recognition.compare_faces(known_face_encodings, unknown_encoding)
        distances = face_recognition.face_distance(known_face_encodings, unknown_encoding)
        
        if True in matches:
            best_match_index = np.argmin(distances)
            return f"匹配成功！相似度距离: {distances[best_match_index]:.3f}"
    
    return "未找到匹配的人脸"

# 使用示例
result = recognize_face("known_person.jpg", "test_image.jpg")
print(result)
```

### 多人脸识别系统

```python
def build_face_database(image_folder):
    """构建人脸数据库"""
    import os
    
    known_face_encodings = []
    known_face_names = []
    
    for filename in os.listdir(image_folder):
        if filename.endswith(('.jpg', '.jpeg', '.png')):
            # 使用文件名作为人名
            name = os.path.splitext(filename)[0]
            
            # 加载并编码人脸
            image_path = os.path.join(image_folder, filename)
            image = face_recognition.load_image_file(image_path)
            encodings = face_recognition.face_encodings(image)
            
            if encodings:
                known_face_encodings.append(encodings[0])
                known_face_names.append(name)
    
    return known_face_encodings, known_face_names

def identify_faces_in_image(image_path, known_encodings, known_names):
    """识别图像中的所有人脸"""
    image = face_recognition.load_image_file(image_path)
    face_locations = face_recognition.face_locations(image)
    face_encodings = face_recognition.face_encodings(image, face_locations)
    
    results = []
    for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
        matches = face_recognition.compare_faces(known_encodings, face_encoding)
        name = "未知"
        
        if True in matches:
            distances = face_recognition.face_distance(known_encodings, face_encoding)
            best_match_index = np.argmin(distances)
            if matches[best_match_index]:
                name = known_names[best_match_index]
        
        results.append({
            'name': name,
            'location': (top, right, bottom, left),
            'confidence': 1 - distances[best_match_index] if True in matches else 0
        })
    
    return results
```

## ⚡ 性能优化建议

1. **预先检测人脸位置**：如果要多次处理同一图像，先检测人脸位置可以加速后续操作
2. **调整上采样次数**：根据图像质量调整 `number_of_times_to_upsample`
3. **选择合适的模型**：CPU环境用HOG，GPU环境用CNN
4. **批处理**：处理多张图像时使用 `batch_face_locations`
5. **调整容忍度**：根据应用场景调整人脸比较的容忍度

## 🚨 常见错误处理

```python
def safe_face_recognition(image_path):
    """安全的人脸识别函数"""
    try:
        image = face_recognition.load_image_file(image_path)
        face_locations = face_recognition.face_locations(image)
        
        if not face_locations:
            return {"error": "未检测到人脸"}
        
        face_encodings = face_recognition.face_encodings(image, face_locations)
        
        if not face_encodings:
            return {"error": "无法生成人脸编码"}
        
        return {
            "success": True,
            "face_count": len(face_locations),
            "locations": face_locations,
            "encodings": face_encodings
        }
        
    except Exception as e:
        return {"error": f"处理失败: {str(e)}"}
```
