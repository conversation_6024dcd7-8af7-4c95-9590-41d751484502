#!/usr/bin/env python3
"""
Face Recognition FastAPI Web Service
基于 FastAPI 的完整人脸识别 Web 服务

功能特性:
- 人脸检测
- 人脸识别
- 人脸比较
- 面部特征检测
- 人脸数据库管理
- 批量处理
- 实时 API 文档
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, Query
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import face_recognition
import numpy as np
import json
import os
import io
import base64
from PIL import Image
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="Face Recognition API",
    description="完整的人脸识别 Web 服务 API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局配置
ALLOWED_EXTENSIONS = {'webp', 'png', 'jpg', 'jpeg', 'gif', 'bmp'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
FACE_DATABASE_FILE = "face_database.json"

# 数据模型
class FaceDetectionResponse(BaseModel):
    success: bool
    message: str
    face_count: int
    faces: List[Dict[str, Any]]
    processing_time: float

class FaceRecognitionResponse(BaseModel):
    success: bool
    message: str
    results: List[Dict[str, Any]]
    processing_time: float

class FaceComparisonResponse(BaseModel):
    success: bool
    message: str
    is_match: bool
    confidence: float
    distance: float
    processing_time: float

class PersonModel(BaseModel):
    name: str
    description: Optional[str] = None

class DatabaseResponse(BaseModel):
    success: bool
    message: str
    person_count: int
    persons: List[str]

# 全局变量
face_database = {
    "encodings": [],
    "names": [],
    "metadata": []
}

def load_face_database():
    """加载人脸数据库"""
    global face_database
    try:
        if os.path.exists(FACE_DATABASE_FILE):
            with open(FACE_DATABASE_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                face_database["encodings"] = [np.array(enc) for enc in data.get("encodings", [])]
                face_database["names"] = data.get("names", [])
                face_database["metadata"] = data.get("metadata", [])
            logger.info(f"已加载人脸数据库，包含 {len(face_database['names'])} 个人员")
    except Exception as e:
        logger.error(f"加载人脸数据库失败: {e}")

def save_face_database():
    """保存人脸数据库"""
    try:
        data = {
            "encodings": [enc.tolist() for enc in face_database["encodings"]],
            "names": face_database["names"],
            "metadata": face_database["metadata"]
        }
        with open(FACE_DATABASE_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info("人脸数据库已保存")
    except Exception as e:
        logger.error(f"保存人脸数据库失败: {e}")

def validate_image_file(file: UploadFile) -> bool:
    """验证上传的图像文件"""
    if not file.filename:
        return False
    
    # 检查文件扩展名
    extension = file.filename.split('.')[-1].lower()
    if extension not in ALLOWED_EXTENSIONS:
        return False
    
    # 检查文件大小
    if hasattr(file, 'size') and file.size > MAX_FILE_SIZE:
        return False
    
    return True

def process_uploaded_image(file: UploadFile) -> np.ndarray:
    """处理上传的图像文件"""
    try:
        # 读取文件内容
        contents = file.file.read()
        
        # 使用 PIL 打开图像
        image = Image.open(io.BytesIO(contents))
        
        # 转换为 RGB 格式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 转换为 numpy 数组
        return np.array(image)
    
    except Exception as e:
        logger.error(f"处理图像失败: {e}")
        raise HTTPException(status_code=400, detail=f"图像处理失败: {str(e)}")

# 启动时加载数据库
@app.on_event("startup")
async def startup_event():
    load_face_database()
    logger.info("Face Recognition API 服务已启动")

@app.on_event("shutdown")
async def shutdown_event():
    save_face_database()
    logger.info("Face Recognition API 服务已关闭")

# API 路由
@app.get("/", response_class=HTMLResponse)
async def root():
    """主页 - 显示 API 使用说明"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Face Recognition API</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .method { color: #fff; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
            .get { background: #61affe; }
            .post { background: #49cc90; }
            .delete { background: #f93e3e; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎭 Face Recognition API</h1>
            <p>完整的人脸识别 Web 服务 API</p>
            
            <h2>📚 API 文档</h2>
            <p><a href="/docs" target="_blank">Swagger UI 文档</a> | <a href="/redoc" target="_blank">ReDoc 文档</a></p>
            
            <h2>🔧 主要功能</h2>
            <div class="endpoint">
                <span class="method post">POST</span> <strong>/detect</strong> - 人脸检测
                <p>上传图片，检测其中的人脸位置和数量</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span> <strong>/recognize</strong> - 人脸识别
                <p>识别图片中的人脸身份（需要先添加到数据库）</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span> <strong>/compare</strong> - 人脸比较
                <p>比较两张图片中的人脸是否为同一人</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span> <strong>/landmarks</strong> - 面部特征检测
                <p>检测人脸的详细特征点（眼睛、鼻子、嘴巴等）</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span> <strong>/database/add</strong> - 添加人员
                <p>将新人员的人脸添加到识别数据库</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span> <strong>/database</strong> - 查看数据库
                <p>查看当前数据库中的所有人员</p>
            </div>
            
            <div class="endpoint">
                <span class="method delete">DELETE</span> <strong>/database/{name}</strong> - 删除人员
                <p>从数据库中删除指定人员</p>
            </div>
            
            <h2>💡 使用示例</h2>
            <pre><code># 人脸检测 (支持 webp, png, jpg, jpeg, gif, bmp 格式)
curl -X POST "http://localhost:8000/detect" -F "file=@photo.webp"

# 添加人员到数据库
curl -X POST "http://localhost:8000/database/add" -F "file=@person.png" -F "name=张三"

# 人脸识别
curl -X POST "http://localhost:8000/recognize" -F "file=@test.jpg"</code></pre>
        </div>
    </body>
    </html>
    """
    return html_content

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database_size": len(face_database["names"])
    }

@app.post("/detect", response_model=FaceDetectionResponse)
async def detect_faces(
    file: UploadFile = File(...),
    model: str = Query("hog", description="检测模型: hog (快速) 或 cnn (准确)"),
    upsample: int = Query(1, description="上采样次数，越高检测越精确")
):
    """
    人脸检测 API

    上传图片，检测其中的人脸位置和数量
    """
    start_time = time.time()

    try:
        # 验证文件
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="无效的图像文件")

        # 处理图像
        image = process_uploaded_image(file)

        # 检测人脸
        face_locations = face_recognition.face_locations(
            image,
            number_of_times_to_upsample=upsample,
            model=model
        )

        # 构建结果
        faces = []
        for i, (top, right, bottom, left) in enumerate(face_locations):
            faces.append({
                "face_id": i + 1,
                "location": {
                    "top": int(top),
                    "right": int(right),
                    "bottom": int(bottom),
                    "left": int(left)
                },
                "size": {
                    "width": int(right - left),
                    "height": int(bottom - top)
                }
            })

        processing_time = time.time() - start_time

        return FaceDetectionResponse(
            success=True,
            message=f"成功检测到 {len(face_locations)} 个人脸",
            face_count=len(face_locations),
            faces=faces,
            processing_time=round(processing_time, 3)
        )

    except Exception as e:
        logger.error(f"人脸检测失败: {e}")
        return FaceDetectionResponse(
            success=False,
            message=f"检测失败: {str(e)}",
            face_count=0,
            faces=[],
            processing_time=time.time() - start_time
        )

@app.post("/recognize", response_model=FaceRecognitionResponse)
async def recognize_faces(
    file: UploadFile = File(...),
    tolerance: float = Query(0.6, description="识别容忍度，越小越严格"),
    model: str = Query("hog", description="检测模型: hog 或 cnn")
):
    """
    人脸识别 API

    识别图片中的人脸身份（需要先添加到数据库）
    """
    start_time = time.time()

    try:
        # 检查数据库是否为空
        if not face_database["names"]:
            raise HTTPException(status_code=400, detail="人脸数据库为空，请先添加人员")

        # 验证文件
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="无效的图像文件")

        # 处理图像
        image = process_uploaded_image(file)

        # 检测人脸
        face_locations = face_recognition.face_locations(image, model=model)
        face_encodings = face_recognition.face_encodings(image, face_locations)

        results = []
        for i, (face_encoding, location) in enumerate(zip(face_encodings, face_locations)):
            # 与数据库中的人脸比较
            matches = face_recognition.compare_faces(
                face_database["encodings"],
                face_encoding,
                tolerance=tolerance
            )
            distances = face_recognition.face_distance(face_database["encodings"], face_encoding)

            name = "未知人员"
            confidence = 0.0
            best_distance = float('inf')

            if True in matches:
                best_match_index = np.argmin(distances)
                if matches[best_match_index]:
                    name = face_database["names"][best_match_index]
                    best_distance = distances[best_match_index]
                    confidence = 1 - best_distance

            top, right, bottom, left = location
            results.append({
                "face_id": i + 1,
                "name": name,
                "confidence": round(confidence, 3),
                "distance": round(best_distance, 3),
                "location": {
                    "top": int(top),
                    "right": int(right),
                    "bottom": int(bottom),
                    "left": int(left)
                }
            })

        processing_time = time.time() - start_time

        return FaceRecognitionResponse(
            success=True,
            message=f"成功识别 {len(results)} 个人脸",
            results=results,
            processing_time=round(processing_time, 3)
        )

    except Exception as e:
        logger.error(f"人脸识别失败: {e}")
        return FaceRecognitionResponse(
            success=False,
            message=f"识别失败: {str(e)}",
            results=[],
            processing_time=time.time() - start_time
        )

@app.post("/compare", response_model=FaceComparisonResponse)
async def compare_faces(
    file1: UploadFile = File(..., description="第一张图片"),
    file2: UploadFile = File(..., description="第二张图片"),
    tolerance: float = Query(0.6, description="比较容忍度")
):
    """
    人脸比较 API

    比较两张图片中的人脸是否为同一人
    """
    start_time = time.time()

    try:
        # 验证文件
        if not validate_image_file(file1) or not validate_image_file(file2):
            raise HTTPException(status_code=400, detail="无效的图像文件")

        # 处理图像
        image1 = process_uploaded_image(file1)
        image2 = process_uploaded_image(file2)

        # 获取人脸编码
        encodings1 = face_recognition.face_encodings(image1)
        encodings2 = face_recognition.face_encodings(image2)

        if not encodings1:
            raise HTTPException(status_code=400, detail="第一张图片中未检测到人脸")
        if not encodings2:
            raise HTTPException(status_code=400, detail="第二张图片中未检测到人脸")

        # 比较人脸
        encoding1 = encodings1[0]
        encoding2 = encodings2[0]

        matches = face_recognition.compare_faces([encoding1], encoding2, tolerance=tolerance)
        distance = face_recognition.face_distance([encoding1], encoding2)[0]

        is_match = matches[0]
        confidence = 1 - distance if distance < 1 else 0

        processing_time = time.time() - start_time

        return FaceComparisonResponse(
            success=True,
            message="比较完成",
            is_match=is_match,
            confidence=round(confidence, 3),
            distance=round(distance, 3),
            processing_time=round(processing_time, 3)
        )

    except Exception as e:
        logger.error(f"人脸比较失败: {e}")
        return FaceComparisonResponse(
            success=False,
            message=f"比较失败: {str(e)}",
            is_match=False,
            confidence=0.0,
            distance=float('inf'),
            processing_time=time.time() - start_time
        )

@app.post("/landmarks")
async def detect_landmarks(
    file: UploadFile = File(...),
    model: str = Query("large", description="特征点模型: large (68点) 或 small (5点)")
):
    """
    面部特征检测 API

    检测人脸的详细特征点（眼睛、鼻子、嘴巴等）
    """
    start_time = time.time()

    try:
        # 验证文件
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="无效的图像文件")

        # 处理图像
        image = process_uploaded_image(file)

        # 检测面部特征
        face_landmarks_list = face_recognition.face_landmarks(image, model=model)

        if not face_landmarks_list:
            return {
                "success": False,
                "message": "未检测到人脸特征",
                "landmarks": [],
                "processing_time": round(time.time() - start_time, 3)
            }

        # 处理特征点数据
        results = []
        for i, landmarks in enumerate(face_landmarks_list):
            face_data = {
                "face_id": i + 1,
                "features": {}
            }

            for feature_name, points in landmarks.items():
                face_data["features"][feature_name] = [
                    {"x": int(point[0]), "y": int(point[1])} for point in points
                ]

            results.append(face_data)

        processing_time = time.time() - start_time

        return {
            "success": True,
            "message": f"成功检测到 {len(results)} 个人脸的特征点",
            "landmarks": results,
            "processing_time": round(processing_time, 3)
        }

    except Exception as e:
        logger.error(f"特征检测失败: {e}")
        return {
            "success": False,
            "message": f"检测失败: {str(e)}",
            "landmarks": [],
            "processing_time": round(time.time() - start_time, 3)
        }

# 数据库管理 API
@app.get("/database", response_model=DatabaseResponse)
async def get_database():
    """
    查看人脸数据库

    返回当前数据库中的所有人员信息
    """
    return DatabaseResponse(
        success=True,
        message="数据库查询成功",
        person_count=len(face_database["names"]),
        persons=face_database["names"]
    )

@app.post("/database/add")
async def add_person_to_database(
    file: UploadFile = File(...),
    name: str = Form(..., description="人员姓名"),
    description: str = Form("", description="人员描述")
):
    """
    添加人员到数据库

    将新人员的人脸添加到识别数据库
    """
    start_time = time.time()

    try:
        # 检查姓名是否已存在
        if name in face_database["names"]:
            raise HTTPException(status_code=400, detail=f"人员 '{name}' 已存在于数据库中")

        # 验证文件
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="无效的图像文件")

        # 处理图像
        image = process_uploaded_image(file)

        # 获取人脸编码
        face_encodings = face_recognition.face_encodings(image)

        if not face_encodings:
            raise HTTPException(status_code=400, detail="图片中未检测到人脸")

        if len(face_encodings) > 1:
            raise HTTPException(status_code=400, detail="图片中检测到多个人脸，请使用只包含一个人脸的图片")

        # 添加到数据库
        face_database["encodings"].append(face_encodings[0])
        face_database["names"].append(name)
        face_database["metadata"].append({
            "description": description,
            "added_time": datetime.now().isoformat(),
            "filename": file.filename
        })

        # 保存数据库
        save_face_database()

        processing_time = time.time() - start_time

        return {
            "success": True,
            "message": f"成功添加人员 '{name}' 到数据库",
            "person_count": len(face_database["names"]),
            "processing_time": round(processing_time, 3)
        }

    except Exception as e:
        logger.error(f"添加人员失败: {e}")
        return {
            "success": False,
            "message": f"添加失败: {str(e)}",
            "person_count": len(face_database["names"]),
            "processing_time": round(time.time() - start_time, 3)
        }

@app.delete("/database/{name}")
async def delete_person_from_database(name: str):
    """
    从数据库删除人员

    根据姓名从数据库中删除指定人员
    """
    try:
        if name not in face_database["names"]:
            raise HTTPException(status_code=404, detail=f"人员 '{name}' 不存在于数据库中")

        # 找到索引并删除
        index = face_database["names"].index(name)
        face_database["encodings"].pop(index)
        face_database["names"].pop(index)
        face_database["metadata"].pop(index)

        # 保存数据库
        save_face_database()

        return {
            "success": True,
            "message": f"成功删除人员 '{name}'",
            "person_count": len(face_database["names"])
        }

    except Exception as e:
        logger.error(f"删除人员失败: {e}")
        return {
            "success": False,
            "message": f"删除失败: {str(e)}",
            "person_count": len(face_database["names"])
        }

@app.delete("/database/clear")
async def clear_database():
    """
    清空数据库

    删除数据库中的所有人员
    """
    try:
        face_database["encodings"].clear()
        face_database["names"].clear()
        face_database["metadata"].clear()

        # 保存数据库
        save_face_database()

        return {
            "success": True,
            "message": "数据库已清空",
            "person_count": 0
        }

    except Exception as e:
        logger.error(f"清空数据库失败: {e}")
        return {
            "success": False,
            "message": f"清空失败: {str(e)}",
            "person_count": len(face_database["names"])
        }

# 批量处理 API
@app.post("/batch/detect")
async def batch_detect_faces(
    files: List[UploadFile] = File(...),
    model: str = Query("hog", description="检测模型")
):
    """
    批量人脸检测

    同时处理多张图片的人脸检测
    """
    start_time = time.time()
    results = []

    for i, file in enumerate(files):
        try:
            if not validate_image_file(file):
                results.append({
                    "file_index": i,
                    "filename": file.filename,
                    "success": False,
                    "message": "无效的图像文件",
                    "face_count": 0
                })
                continue

            image = process_uploaded_image(file)
            face_locations = face_recognition.face_locations(image, model=model)

            results.append({
                "file_index": i,
                "filename": file.filename,
                "success": True,
                "message": f"检测到 {len(face_locations)} 个人脸",
                "face_count": len(face_locations),
                "faces": [
                    {
                        "location": {
                            "top": int(top), "right": int(right),
                            "bottom": int(bottom), "left": int(left)
                        }
                    } for top, right, bottom, left in face_locations
                ]
            })

        except Exception as e:
            results.append({
                "file_index": i,
                "filename": file.filename,
                "success": False,
                "message": f"处理失败: {str(e)}",
                "face_count": 0
            })

    processing_time = time.time() - start_time

    return {
        "success": True,
        "message": f"批量处理完成，共处理 {len(files)} 张图片",
        "results": results,
        "processing_time": round(processing_time, 3)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
