# Face Recognition API Docker Image
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    libopenblas-dev \
    liblapack-dev \
    libx11-dev \
    libgtk-3-dev \
    libboost-python-dev \
    libboost-system-dev \
    libboost-filesystem-dev \
    pkg-config \
    wget \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 升级 pip
RUN pip install --upgrade pip

# 安装 dlib (从源码编译以确保兼容性)
RUN git clone https://github.com/davisking/dlib.git && \
    cd dlib && \
    python setup.py install && \
    cd .. && \
    rm -rf dlib

# 复制 requirements 文件并安装基础依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 安装 face_recognition 库
RUN pip install --no-cache-dir face_recognition

# 安装 FastAPI 相关依赖
RUN pip install --no-cache-dir \
    fastapi==0.115.12 \
    uvicorn==0.34.3 \
    python-multipart==0.0.20 \
    requests==2.32.4

# 复制应用代码
COPY face_recognition_api.py .
COPY start_api_server.py .
COPY test_api_client.py .

# 复制示例图片 (用于测试)
COPY examples/ ./examples/

# 创建数据目录
RUN mkdir -p /app/data

# 创建非 root 用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "face_recognition_api:app", "--host", "0.0.0.0", "--port", "8000"]
